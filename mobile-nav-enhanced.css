/* Enhanced Mobile Navigation */

/* Mobile Menu Button */
.mobile-menu-btn {
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #000080, #1e40af);
    border-radius: 50%;
    box-shadow: 0 4px 10px rgba(0, 0, 128, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-menu-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 15px rgba(0, 0, 128, 0.4);
}

.mobile-menu-btn i {
    color: white;
    font-size: 1.5rem;
}

/* Mobile Menu */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 85%;
    max-width: 400px;
    height: 100vh;
    background: linear-gradient(135deg, #f8fafc, #ffffff);
    z-index: 1000;
    transition: all 0.4s cubic-bezier(0.77, 0, 0.175, 1);
    box-shadow: -5px 0 30px rgba(0, 0, 0, 0.15);
    overflow-y: auto;
}

.mobile-menu.active {
    right: 0;
}

/* Overlay when menu is open */
.mobile-menu::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s ease;
}

.mobile-menu.active::before {
    opacity: 1;
    visibility: visible;
}

.mobile-menu-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 30px 0;
}

.mobile-menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 25px 20px;
    border-bottom: 1px solid rgba(0, 0, 128, 0.1);
    margin-bottom: 20px;
}

.mobile-logo {
    height: 60px;
    width: auto;
}

#menu-close {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 128, 0.1);
    border-radius: 50%;
    color: #000080;
    font-size: 1.3rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

#menu-close:hover {
    background: rgba(0, 0, 128, 0.2);
    transform: rotate(90deg);
}

.mobile-nav-links {
    list-style: none;
    padding: 0 15px;
    margin: 0;
    flex: 1;
}

.mobile-nav-links li {
    margin-bottom: 5px;
}

.mobile-nav-links li a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #1e293b;
    text-decoration: none;
    font-weight: 500;
    font-size: 1.1rem;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.mobile-nav-links li a i {
    margin-right: 15px;
    font-size: 1.3rem;
    color: #000080;
    transition: all 0.3s ease;
}

.mobile-nav-links li a:hover {
    background: rgba(0, 0, 128, 0.05);
    color: #000080;
    transform: translateX(5px);
}

.mobile-nav-links li a:hover i {
    transform: scale(1.2);
}

.mobile-nav-links li a.active {
    background: rgba(0, 0, 128, 0.1);
    color: #000080;
    font-weight: 600;
}

/* Services dropdown in mobile menu */
.mobile-dropdown {
    margin-bottom: 5px;
}

.mobile-dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    color: #1e293b;
    text-decoration: none;
    font-weight: 500;
    font-size: 1.1rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-dropdown-toggle i.ri-service-line {
    margin-right: 15px;
    font-size: 1.3rem;
    color: #000080;
}

.mobile-dropdown-toggle i.ri-arrow-down-s-line {
    transition: transform 0.3s ease;
}

.mobile-dropdown.active .mobile-dropdown-toggle i.ri-arrow-down-s-line {
    transform: rotate(180deg);
}

.mobile-dropdown-toggle:hover {
    background: rgba(0, 0, 128, 0.05);
    color: #000080;
}

.mobile-dropdown-content {
    display: none;
    padding: 5px 0 5px 35px;
}

.mobile-dropdown.active .mobile-dropdown-content {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.mobile-dropdown-content a {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: #1e293b;
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    border-radius: 10px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

.mobile-dropdown-content a i {
    margin-right: 12px;
    font-size: 1.1rem;
    color: #000080;
}

.mobile-dropdown-content a:hover {
    background: rgba(0, 0, 128, 0.05);
    color: #000080;
    transform: translateX(5px);
}

.mobile-apply-btn {
    margin: 20px 25px 0;
    background: none;
    border: none;
    padding: 0;
}

.mobile-apply-btn a {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: linear-gradient(135deg, #000080, #1e40af);
    color: white;
    padding: 16px 25px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    text-decoration: none;
    box-shadow: 0 8px 20px rgba(0, 0, 128, 0.3);
    transition: all 0.3s ease;
    width: 100%;
}

.mobile-apply-btn a i {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.mobile-apply-btn a:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 25px rgba(0, 0, 128, 0.4);
}

.mobile-apply-btn a:hover i {
    transform: translateX(5px);
}

/* Contact info in mobile menu */
.mobile-contact-info {
    padding: 20px 25px;
    margin-top: 20px;
    border-top: 1px solid rgba(0, 0, 128, 0.1);
}

.mobile-contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.mobile-contact-item i {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 128, 0.1);
    border-radius: 50%;
    color: #000080;
    font-size: 1.1rem;
    margin-right: 15px;
}

.mobile-contact-item a {
    color: #1e293b;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.mobile-contact-item a:hover {
    color: #000080;
}

/* Social links in mobile menu */
.mobile-social-links {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

.mobile-social-links a {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 128, 0.1);
    border-radius: 50%;
    color: #000080;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.mobile-social-links a:hover {
    background: #000080;
    color: white;
    transform: translateY(-3px);
}