/* Enhanced Process Section Styles */
.process-enhanced {
    padding: 100px 0 120px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f7fa 100%);
    position: relative;
    overflow: hidden;
}

.process-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="%23000080" opacity="0.03"/></svg>') repeat;
}

.process-container {
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 15px;
    position: relative;
    z-index: 2;
}

.process-header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
}

.process-header::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), var(--primary-light), var(--primary));
    background-size: 200% 100%;
    border-radius: 3px;
    animation: gradientMove 3s infinite linear;
}

.process-steps {
    display: flex;
    justify-content: center;
    position: relative;
    margin-top: 80px;
    margin-bottom: 40px;
    z-index: 3;
}

.process-timeline {
    position: absolute;
    top: 60px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    border-radius: 4px;
    z-index: 1;
}

.process-timeline::before,
.process-timeline::after {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--primary);
}

.process-timeline::before {
    left: -6px;
}

.process-timeline::after {
    right: -6px;
}

.process-step {
    position: relative;
    width: 20%;
    padding: 0 10px;
    text-align: center;
    z-index: 2;
    display: flex;
    flex-direction: column;
}

.process-step-icon {
    width: 120px;
    height: 120px;
    margin: 0 auto 25px;
    background: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.15);
    border: 2px solid rgba(0, 0, 128, 0.1);
    transition: all 0.4s ease;
}

.process-step-icon::before {
    content: '';
    position: absolute;
    inset: -8px;
    border-radius: 50%;
    border: 2px dashed var(--primary);
    opacity: 0.3;
    transition: all 0.4s ease;
}

.process-step:hover .process-step-icon {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 128, 0.2);
    border-color: var(--primary);
}

.process-step:hover .process-step-icon::before {
    opacity: 0.6;
    animation: spin 10s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.process-step-number {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 700;
    box-shadow: 0 5px 15px rgba(0, 0, 128, 0.3);
    border: 2px solid #fff;
    z-index: 2;
}

.process-step-inner {
    width: 90px;
    height: 90px;
    background: linear-gradient(135deg, rgba(0, 0, 128, 0.05), rgba(0, 0, 128, 0.1));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s ease;
}

.process-step:hover .process-step-inner {
    background: linear-gradient(135deg, rgba(0, 0, 128, 0.1), rgba(0, 0, 128, 0.15));
    transform: scale(1.1);
}

.process-step-icon i {
    font-size: 36px;
    color: var(--primary);
    transition: all 0.4s ease;
}

.process-step:hover .process-step-icon i {
    transform: scale(1.2);
}

.process-step-content {
    padding: 20px;
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.1);
    border: 2px solid transparent;
    transition: all 0.4s ease;
    height: auto;
    min-height: 120px;
}

.process-step:hover .process-step-content {
    border-color: rgba(0, 0, 128, 0.1);
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 128, 0.15);
}

.process-step-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.process-step:hover .process-step-title {
    color: var(--primary-dark);
}

.process-step-description {
    font-size: 14px;
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.process-action {
    margin-top: 60px;
    text-align: center;
    position: relative;
    z-index: 10;
}

.process-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: #fff;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 700;
    text-decoration: none;
    transition: all 0.4s ease;
    box-shadow: 0 10px 25px rgba(0, 0, 128, 0.2);
    position: relative;
    overflow: hidden;
    margin-top: 20px;
}

.process-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.process-btn:hover::before {
    left: 100%;
}

.process-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 128, 0.3);
}

.process-btn i {
    font-size: 18px;
    transition: transform 0.3s ease;
}

.process-btn:hover i {
    transform: translateX(5px);
}

/* Animation for steps */
.process-step {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.process-step.animated {
    opacity: 1;
    transform: translateY(0);
}

.process-step:nth-child(1) { transition-delay: 0.1s; }
.process-step:nth-child(2) { transition-delay: 0.2s; }
.process-step:nth-child(3) { transition-delay: 0.3s; }
.process-step:nth-child(4) { transition-delay: 0.4s; }
.process-step:nth-child(5) { transition-delay: 0.5s; }

/* Responsive styles */
@media (max-width: 992px) {
    .process-steps {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .process-step {
        width: 33.33%;
        margin-bottom: 40px;
    }
    
    .process-timeline {
        display: none;
    }
}

@media (max-width: 768px) {
    .process-step {
        width: 50%;
    }
}

@media (max-width: 576px) {
    .process-step {
        width: 100%;
    }
    
    .process-step-icon {
        width: 100px;
        height: 100px;
    }
    
    .process-step-inner {
        width: 80px;
        height: 80px;
    }
    
    .process-step-icon i {
        font-size: 30px;
    }
}