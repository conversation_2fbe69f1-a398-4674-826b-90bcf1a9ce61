/* Optimized Services Section */
.optimized-services-section {
    padding: 80px 0;
    background: #f8fafc;
    position: relative;
    overflow: hidden;
}

.optimized-services-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.optimized-services-header {
    text-align: center;
    margin-bottom: 50px;
}

.optimized-services-header h2 {
    font-size: 2.5rem;
    color: #000080;
    margin-bottom: 15px;
    font-weight: 700;
    position: relative;
    display: inline-block;
}

.optimized-services-header h2::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: #3b82f6;
}

.optimized-services-header p {
    color: #64748b;
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto;
}

.services-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.service-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 20px rgba(0, 0, 128, 0.05);
    overflow: hidden;
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 128, 0.1);
}

.service-header {
    padding: 20px;
    background: #000080;
    color: white;
    position: relative;
}

.service-header.featured {
    background: linear-gradient(135deg, #000080 0%, #3b82f6 100%);
}

.service-header .service-number {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 1.5rem;
    font-weight: 700;
    opacity: 0.3;
}

.service-header .service-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.service-header .service-icon i {
    font-size: 1.8rem;
    color: white;
}

.service-header h3 {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.service-content {
    padding: 20px;
}

.service-content p {
    color: #64748b;
    margin-bottom: 20px;
}

.service-features {
    margin-bottom: 20px;
}

.service-feature {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: #64748b;
}

.service-feature i {
    color: #3b82f6;
    margin-right: 10px;
}

.service-btn {
    display: block;
    background: #000080;
    color: white;
    text-align: center;
    padding: 12px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.service-btn:hover {
    background: #3b82f6;
}

.featured-tag {
    position: absolute;
    top: -10px;
    right: 20px;
    background: #ff6b6b;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 0 5px 10px rgba(255, 107, 107, 0.3);
    z-index: 1;
}

/* Additional Services */
.additional-services {
    margin-top: 40px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.additional-service {
    background: white;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 128, 0.05);
    transition: all 0.3s ease;
}

.additional-service:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 128, 0.1);
}

.additional-service-icon {
    width: 40px;
    height: 40px;
    background: #f0f9ff;
    color: #000080;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.additional-service-content {
    flex-grow: 1;
}

.additional-service-content h4 {
    font-size: 1.1rem;
    color: #000080;
    margin-bottom: 5px;
}

.additional-service-content a {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
}

.additional-service-content a i {
    margin-left: 5px;
    transition: all 0.3s ease;
}

.additional-service-content a:hover i {
    transform: translateX(3px);
}

/* Responsive styles */
@media (max-width: 992px) {
    .services-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .additional-services {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .optimized-services-section {
        padding: 60px 0;
    }
    
    .optimized-services-header h2 {
        font-size: 2rem;
    }
    
    .services-cards {
        grid-template-columns: 1fr;
    }
    
    .additional-services {
        grid-template-columns: 1fr;
    }
}