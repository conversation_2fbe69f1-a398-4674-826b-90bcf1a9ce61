/* Professional About Page Design */

/* Hero Section - Modern Corporate Style */
.professional-hero {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
    padding: 120px 0 100px;
    position: relative;
    overflow: hidden;
    color: white;
}

.professional-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-text {
    animation: slideInLeft 1s ease-out;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.15);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    line-height: 1.6;
    margin-bottom: 30px;
    opacity: 0.9;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    display: block;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.hero-visual {
    position: relative;
    animation: slideInRight 1s ease-out;
}

.hero-image-wrapper {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.hero-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.hero-image:hover {
    transform: scale(1.05);
}

.floating-card {
    position: absolute;
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    color: #1e3a8a;
    animation: float 3s ease-in-out infinite;
}

.floating-card-1 {
    top: 20px;
    right: -20px;
    animation-delay: 0s;
}

.floating-card-2 {
    bottom: 20px;
    left: -20px;
    animation-delay: 1.5s;
}

.card-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #3b82f6, #1e3a8a);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-bottom: 10px;
}

.card-title {
    font-weight: 700;
    margin-bottom: 5px;
}

.card-text {
    font-size: 0.9rem;
    opacity: 0.7;
}

/* Company Story Section */
.company-story {
    padding: 100px 0;
    background: #f8fafc;
}

.story-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.story-header {
    text-align: center;
    margin-bottom: 80px;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #e0e7ff;
    color: #1e3a8a;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.section-title {
    font-size: 2.8rem;
    font-weight: 800;
    color: #1e3a8a;
    margin-bottom: 20px;
    line-height: 1.2;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #64748b;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.story-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #475569;
}

.story-text p {
    margin-bottom: 20px;
}

.story-visual {
    position: relative;
}

.story-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Values Section */
.values-section {
    padding: 100px 0;
    background: white;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.value-card {
    background: white;
    padding: 40px 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.value-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.value-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #3b82f6, #1e3a8a);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 2rem;
}

.value-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.value-description {
    color: #64748b;
    line-height: 1.6;
}

/* Animations */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 80px 0;
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 20px;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-btn {
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.cta-btn-primary {
    background: white;
    color: #1e3a8a;
}

.cta-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.3);
}

.cta-btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.cta-btn-secondary:hover {
    background: white;
    color: #1e3a8a;
    transform: translateY(-3px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .story-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .values-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .floating-card {
        position: static;
        margin: 20px 0;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}
