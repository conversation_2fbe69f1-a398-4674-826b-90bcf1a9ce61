// Force enable scrolling on all pages
window.onload = function() {
    // Remove any scroll blockers
    document.body.style.overflow = 'auto';
    document.documentElement.style.overflow = 'auto';
    document.body.style.height = 'auto';
    document.documentElement.style.height = 'auto';
    
    // Remove any event listeners that might be blocking wheel events
    window.removeEventListener('wheel', preventDefault, { passive: false });
    document.removeEventListener('wheel', preventDefault, { passive: false });
    
    // Force enable wheel scrolling
    document.addEventListener('wheel', function(e) {
        window.scrollBy(0, e.deltaY);
    });
    
    function preventDefault(e) {
        e.preventDefault();
    }
    
    // Disable any Lenis or Locomotive scroll instances
    if (window.lenis) {
        window.lenis.destroy();
        window.lenis = null;
    }
    
    console.log('Scrolling forcefully enabled');
}