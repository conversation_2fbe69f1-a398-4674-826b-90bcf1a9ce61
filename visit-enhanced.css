/* Enhanced Visit Page Styles */
:root {
    --primary: #000080;
    --primary-light: #3b82f6;
    --accent: #f59e0b;
    --text-dark: #1e293b;
    --text-light: #64748b;
    --bg-light: #f8fafc;
    --bg-dark: #0f172a;
    --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 25px rgba(0, 0, 0, 0.15);
}

/* Hero Section */
.visit-hero-section {
    position: relative;
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
    overflow: hidden;
}

.visit-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.visit-hero-content {
    display: flex;
    align-items: center;
    gap: 60px;
}

.hero-left {
    flex: 1;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: rgba(0, 0, 128, 0.1);
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
}

.badge-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: var(--primary);
    border-radius: 50%;
    color: white;
}

.badge-text {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--primary);
    letter-spacing: 1px;
    text-transform: uppercase;
}

.hero-title {
    font-size: 3.5rem;
    line-height: 1.2;
    margin-bottom: 20px;
    color: var(--text-dark);
}

.title-highlight {
    color: var(--primary);
}

.hero-subtitle {
    font-size: 1.2rem;
    line-height: 1.6;
    color: var(--text-light);
    margin-bottom: 30px;
}

.hero-features {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 30px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    background: white;
    padding: 10px 20px;
    border-radius: 50px;
    box-shadow: var(--shadow-sm);
}

.feature-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: var(--primary);
    border-radius: 50%;
    color: white;
}

.hero-right {
    flex: 1;
}

.main-image {
    width: 100%;
    height: 500px;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}

.main-image:hover img {
    transform: scale(1.05);
}

/* Services Section */
.visit-services-section {
    padding: 80px 0;
    background: var(--bg-light);
}

.visit-services-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(0, 0, 128, 0.1);
    color: var(--primary);
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
    font-weight: 600;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 700px;
    margin: 0 auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}

.service-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.service-icon {
    width: 70px;
    height: 70px;
    background: var(--primary);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 30px auto 20px;
    color: white;
    font-size: 2rem;
}

.service-card h3 {
    font-size: 1.8rem;
    color: var(--text-dark);
    margin-bottom: 15px;
    text-align: center;
}

.service-card p {
    font-size: 1rem;
    color: var(--text-light);
    margin-bottom: 20px;
    padding: 0 30px;
    text-align: center;
}

.service-card ul {
    list-style: none;
    padding: 0 30px;
    margin-bottom: 30px;
}

.service-card ul li {
    position: relative;
    padding-left: 30px;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.service-card ul li::before {
    content: "✓";
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary);
    font-weight: bold;
}

.service-countries {
    padding: 20px 30px;
    border-top: 1px solid #e2e8f0;
}

.country-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.country-tags span {
    padding: 5px 15px;
    background: #f1f5f9;
    border-radius: 50px;
    font-size: 0.9rem;
    color: var(--text-dark);
}

.service-btn {
    display: block;
    width: calc(100% - 60px);
    margin: 0 30px 30px;
    padding: 15px;
    background: var(--primary);
    color: white;
    text-align: center;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.service-btn:hover {
    background: #000066;
    transform: translateY(-3px);
}

/* Visa Types Section (from Y-Axis) */
.visa-types-section {
    padding: 80px 0;
    background: white;
}

.visa-types-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.visa-types-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.visa-type-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.visa-type-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary);
}

.visa-type-header {
    padding: 20px;
    background: var(--primary);
    color: white;
    text-align: center;
}

.visa-type-header h3 {
    font-size: 1.5rem;
    margin: 0;
}

.visa-type-content {
    padding: 20px;
}

.visa-type-content p {
    color: var(--text-light);
    margin-bottom: 20px;
}

.visa-type-features {
    margin-bottom: 20px;
}

.visa-type-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.visa-type-features ul li {
    position: relative;
    padding-left: 25px;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.visa-type-features ul li::before {
    content: "✓";
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary);
}

.visa-type-btn {
    display: block;
    width: 100%;
    padding: 12px;
    background: var(--primary);
    color: white;
    text-align: center;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.visa-type-btn:hover {
    background: #000066;
}

/* Process Section (from Y-Axis) */
.visa-process-section {
    padding: 80px 0;
    background: #f8fafc;
}

.visa-process-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.process-steps {
    display: flex;
    justify-content: space-between;
    position: relative;
    margin-top: 60px;
}

.process-steps::before {
    content: "";
    position: absolute;
    top: 40px;
    left: 0;
    width: 100%;
    height: 2px;
    background: #e2e8f0;
    z-index: 1;
}

.process-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    width: 20%;
}

.step-number {
    width: 80px;
    height: 80px;
    background: white;
    border: 2px solid var(--primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 20px;
}

.step-content {
    text-align: center;
}

.step-content h4 {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.step-content p {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* FAQ Section (from Y-Axis) */
.faq-section {
    padding: 80px 0;
    background: white;
}

.faq-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 20px;
}

.faq-list {
    margin-top: 50px;
}

.faq-item {
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 20px;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    cursor: pointer;
}

.faq-question h3 {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin: 0;
}

.faq-question i {
    color: var(--primary);
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.faq-answer {
    padding: 0 0 20px;
    color: var(--text-light);
    line-height: 1.6;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

/* Testimonials Section */
.testimonials-section {
    padding: 80px 0;
    background: #f8fafc;
}

.testimonials-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 50px;
}

.testimonial-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: var(--shadow-md);
}

.testimonial-content {
    position: relative;
    padding-left: 30px;
    margin-bottom: 20px;
}

.testimonial-content::before {
    content: """;
    position: absolute;
    left: 0;
    top: -10px;
    font-size: 3rem;
    color: var(--primary);
    font-family: serif;
    line-height: 1;
}

.testimonial-content p {
    color: var(--text-light);
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.author-image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
}

.author-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-info h4 {
    font-size: 1.1rem;
    color: var(--text-dark);
    margin: 0 0 5px;
}

.author-info p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0;
}

/* CTA Section */
.visa-cta-section {
    padding: 100px 0;
    background: linear-gradient(rgba(0, 0, 128, 0.9), rgba(0, 0, 128, 0.9)), url('https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=1500&q=80');
    background-size: cover;
    background-position: center;
    color: white;
    text-align: center;
}

.visa-cta-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 20px;
}

.visa-cta-container h2 {
    font-size: 3rem;
    margin-bottom: 20px;
}

.visa-cta-container p {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.visa-cta-btn {
    display: inline-block;
    padding: 15px 40px;
    background: white;
    color: var(--primary);
    font-size: 1.2rem;
    font-weight: 600;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.visa-cta-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

/* Responsive styles */
@media (max-width: 992px) {
    .visit-hero-content {
        flex-direction: column;
    }
    
    .hero-left, .hero-right {
        width: 100%;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .main-image {
        height: 400px;
        margin-top: 40px;
    }
    
    .services-grid, .visa-types-grid {
        grid-template-columns: 1fr;
    }
    
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .process-steps {
        flex-direction: column;
        align-items: center;
    }
    
    .process-steps::before {
        display: none;
    }
    
    .process-step {
        width: 100%;
        margin-bottom: 40px;
    }
}