/* Y-Axis Style CSS */
.yaxis-services-section {
    padding: 80px 0;
    background: #f8fafc;
}

.yaxis-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.yaxis-header {
    text-align: center;
    margin-bottom: 50px;
}

.yaxis-header h2 {
    font-size: 2.5rem;
    color: #000080;
    margin-bottom: 15px;
    font-weight: 700;
}

.yaxis-header p {
    font-size: 1.1rem;
    color: #64748b;
    max-width: 700px;
    margin: 0 auto;
}

.yaxis-services-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.yaxis-service-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #e0e7ef;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.07);
    transition: box-shadow 0.3s, transform 0.3s;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.yaxis-service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.13);
    border-color: #000080;
}

.yaxis-service-image {
    height: 180px;
    overflow: hidden;
    position: relative;
}

.yaxis-service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}

.yaxis-service-card:hover .yaxis-service-image img {
    transform: scale(1.1);
}

.yaxis-service-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 128, 0.1), rgba(0, 0, 128, 0.5));
    display: flex;
    align-items: center;
    justify-content: center;
}

.yaxis-service-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #e0e7ef 0%, #fff 100%);
    border: 2px solid #000080;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.10);
}

.yaxis-service-icon i {
    font-size: 1.8rem;
    color: #000080;
}

.yaxis-service-content {
    padding: 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.yaxis-service-title {
    margin-bottom: 15px;
    text-align: center;
}

.yaxis-service-title h3 {
    font-size: 1.5rem;
    color: #000080;
    margin-bottom: 5px;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 0 #e0e7ef;
}

.yaxis-service-title p {
    font-size: 0.9rem;
    color: #64748b;
}

.yaxis-service-features {
    margin-bottom: 20px;
    flex: 1;
}

.yaxis-service-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.yaxis-service-features ul li {
    position: relative;
    padding-left: 25px;
    margin-bottom: 10px;
    font-size: 1rem;
    color: #22223b;
    font-weight: 500;
}

.yaxis-service-features ul li::before {
    content: "✓";
    position: absolute;
    left: 0;
    top: 0;
    color: #000080;
    font-weight: bold;
}

.yaxis-service-footer {
    margin-top: auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 15px;
    border-top: 1px solid #f1f5f9;
}

.yaxis-service-countries {
    display: flex;
    align-items: center;
    gap: 5px;
}

.yaxis-country-flag {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    overflow: hidden;
}

.yaxis-country-flag img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.yaxis-service-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 8px 15px;
    background: linear-gradient(90deg, #000080 60%, #1e3a8a 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: none;
    color: white;
    border-radius: 5px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.yaxis-service-btn:hover {
    background: linear-gradient(90deg, #1e3a8a 60%, #000080 100%);
    color: #fff;
}

.yaxis-service-btn i {
    font-size: 1rem;
    transition: all 0.3s ease;
}

.yaxis-service-btn:hover i {
    transform: translateX(3px);
}

/* Popular Services Section */
.popular-services-section {
    padding: 80px 0;
    background: white;
}

.popular-services-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.popular-service-card {
    background: #f8fafc;
    border-radius: 10px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #f1f5f9;
}

.popular-service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    border-color: #000080;
}

.popular-service-icon {
    width: 70px;
    height: 70px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.popular-service-icon i {
    font-size: 2rem;
    color: #000080;
}

.popular-service-title h3 {
    font-size: 1.2rem;
    color: #000080;
    margin-bottom: 10px;
    font-weight: 600;
}

.popular-service-title p {
    font-size: 0.9rem;
    color: #64748b;
    margin-bottom: 15px;
}

.popular-service-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    color: #000080;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.popular-service-btn:hover {
    color: #000066;
}

.popular-service-btn i {
    font-size: 1rem;
    transition: all 0.3s ease;
}

.popular-service-btn:hover i {
    transform: translateX(3px);
}

/* Responsive styles */
@media (max-width: 992px) {
    .yaxis-services-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .popular-services-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .yaxis-services-grid {
        grid-template-columns: 1fr;
    }
    
    .popular-services-grid {
        grid-template-columns: 1fr;
    }
    
    .yaxis-header h2 {
        font-size: 2rem;
    }
}