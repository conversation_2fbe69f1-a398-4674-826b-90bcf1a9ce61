/* Enhanced About Section Styles */
.about-enhanced {
    padding: 120px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #eef2ff 100%);
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid rgba(0, 0, 128, 0.05);
}

.about-enhanced::before {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background-color: rgba(0, 0, 128, 0.03);
    z-index: 1;
}

.about-enhanced::after {
    content: '';
    position: absolute;
    bottom: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background-color: rgba(0, 0, 128, 0.03);
    z-index: 1;
}

.about-container {
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 15px;
    position: relative;
    z-index: 2;
}

.about-content {
    display: flex;
    align-items: center;
    gap: 50px;
}

.about-image {
    flex: 1;
    position: relative;
}

.about-img-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 128, 0.2);
    border: 8px solid #fff;
    transition: all 0.5s ease;
}

.about-img-container:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 60px rgba(0, 0, 128, 0.25);
}

.about-img {
    width: 100%;
    height: auto;
    display: block;
    transition: all 0.5s ease;
}

.about-img-container:hover .about-img {
    transform: scale(1.05);
}

.about-badge {
    position: absolute;
    top: 25px;
    right: -20px;
    background: linear-gradient(135deg, var(--secondary), var(--primary));
    color: #fff;
    padding: 12px 25px;
    border-radius: 30px;
    font-weight: 700;
    box-shadow: 0 8px 20px rgba(0, 0, 128, 0.25);
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 2;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.about-badge i {
    font-size: 22px;
    animation: spin 4s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.about-text {
    flex: 1;
}

.about-subtitle {
    display: inline-block;
    background-color: rgba(0, 0, 128, 0.1);
    color: var(--primary);
    padding: 5px 15px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 20px;
}

.about-title {
    font-size: 42px;
    font-weight: 500;
    color: var(--primary);
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 18px;
    line-height: 1.2;
}

.about-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 128, 0.2);
}

.about-description {
    font-size: 17px;
    line-height: 1.8;
    color: var(--text-light);
    margin-bottom: 35px;
    max-width: 95%;
}

.about-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
    margin-bottom: 30px;
}

.about-feature {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    transition: all 0.3s ease;
}

.about-feature:hover {
    transform: translateY(-5px);
}

.about-feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 8px 20px rgba(0, 0, 128, 0.2);
    transition: all 0.3s ease;
}

.about-feature:hover .about-feature-icon {
    transform: translateY(-5px);
    box-shadow: 0 12px 25px rgba(0, 0, 128, 0.25);
}

.about-feature-icon i {
    font-size: 28px;
    color: #fff;
    transition: all 0.3s ease;
}

.about-feature:hover .about-feature-icon i {
    transform: scale(1.1);
}

.about-feature-text h5 {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.about-feature:hover .about-feature-text h5 {
    color: var(--secondary);
}

.about-feature-text p {
    font-size: 14px;
    color: var(--text-light);
    line-height: 1.5;
}

.about-action {
    margin-top: 30px;
}

.about-btn {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: #fff;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 700;
    font-size: 16px;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 8px 20px rgba(0, 0, 128, 0.2);
    position: relative;
    overflow: hidden;
}

.about-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
}

.about-btn:hover::before {
    left: 100%;
}

.about-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 128, 0.2);
}

.about-btn i {
    font-size: 18px;
    transition: transform 0.3s ease;
}

.about-btn:hover i {
    transform: translateX(5px);
}

/* Animation for elements */
.about-image, .about-text {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.about-image.animated, .about-text.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive styles */
@media (max-width: 992px) {
    .about-content {
        flex-direction: column;
    }
    
    .about-image {
        margin-bottom: 40px;
        max-width: 500px;
        width: 100%;
    }
    
    .about-badge {
        right: 20px;
    }
    
    .about-title {
        font-size: 32px;
    }
    
    .about-title::after {
        left: 50%;
        transform: translateX(-50%);
    }
    
    .about-text {
        text-align: center;
    }
    
    .about-features {
        grid-template-columns: 1fr;
    }
    
    .about-feature {
        justify-content: center;
        text-align: left;
    }
}

@media (max-width: 768px) {
    .about-enhanced {
        padding: 70px 0;
    }
    
    .about-title {
        font-size: 28px;
    }
    
    .about-features {
        gap: 20px;
    }
}

@media (max-width: 576px) {
    .about-enhanced {
        padding: 50px 0;
    }
    
    .about-title {
        font-size: 24px;
    }
    
    .about-feature-icon {
        width: 40px;
        height: 40px;
    }
    
    .about-feature-icon i {
        font-size: 20px;
    }
    
    .about-feature-text h5 {
        font-size: 16px;
    }
}