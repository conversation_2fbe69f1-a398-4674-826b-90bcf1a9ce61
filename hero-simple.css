/* Simple Hero Section Styles */
.hero-simple {
    background: linear-gradient(to right, rgba(0, 0, 128, 0.8) 0%, rgba(0, 0, 128, 0.7) 100%);
    padding: 80px 0;
    color: #fff;
    position: relative;
    overflow: hidden;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.hero-content {
    display: flex;
    align-items: center;
    gap: 40px;
    flex-direction: row;
}

.hero-text {
    flex: 1;
    max-width: 50%;
}

.hero-subtitle {
    display: inline-block;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-title {
    font-size: 42px;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 20px;
}

.hero-description {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 30px;
    opacity: 0.9;
}

.hero-stats {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
}

.hero-stat {
    display: flex;
    align-items: center;
    gap: 10px;
}

.hero-stat-icon {
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-stat-icon i {
    font-size: 18px;
    color: #fff;
}

.hero-stat-text {
    display: flex;
    flex-direction: column;
}

.hero-stat-value {
    font-size: 20px;
    font-weight: 700;
}

.hero-stat-label {
    font-size: 13px;
    opacity: 0.8;
}

.hero-buttons {
    display: flex;
    gap: 15px;
}

.hero-btn-primary {
    background-color: #fff;
    color: var(--primary);
    padding: 12px 24px;
    border-radius: 30px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.hero-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.hero-btn-secondary {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    padding: 12px 24px;
    border-radius: 30px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.hero-btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.hero-image {
    flex: 1;
    position: relative;
    max-width: 50%;
}

.hero-img-container {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    border: 5px solid rgba(255, 255, 255, 0.1);
}

.hero-img {
    width: 100%;
    height: auto;
    display: block;
}

.hero-badge {
    position: absolute;
    bottom: -15px;
    right: 20px;
    background-color: #fff;
    color: var(--primary);
    padding: 10px 20px;
    border-radius: 30px;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 5;
}

.hero-badge i {
    color: var(--secondary);
}

/* Responsive styles */
@media (max-width: 992px) {
    .hero-content {
        flex-direction: column;
    }
    
    .hero-text {
        text-align: center;
        max-width: 100%;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .hero-image {
        margin-top: 40px;
        max-width: 500px;
        width: 100%;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 32px;
    }
    
    .hero-stats {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .hero-buttons {
        flex-direction: column;
    }
    
    .hero-btn-primary, .hero-btn-secondary {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .hero-simple {
        padding: 60px 0;
    }
    
    .hero-title {
        font-size: 28px;
    }
}