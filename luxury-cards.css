/* Luxury Cards Styles */
.luxury-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8fafc, #f0f9ff);
    position: relative;
}

.luxury-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.luxury-header {
    text-align: center;
    margin-bottom: 70px;
    position: relative;
}

.luxury-header span {
    display: inline-block;
    padding: 8px 20px;
    background: rgba(0, 0, 128, 0.1);
    color: #000080;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 15px;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.luxury-header h2 {
    font-size: 2.8rem;
    color: #000080;
    margin-bottom: 20px;
    font-weight: 700;
    position: relative;
    display: inline-block;
}

.luxury-header h2::after {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(to right, #000080, #3b82f6);
    border-radius: 3px;
}

.luxury-header p {
    font-size: 1.2rem;
    color: #64748b;
    max-width: 700px;
    margin: 30px auto 0;
}

/* Single row layout */
.luxury-row {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 20px;
    scrollbar-width: thin;
    scrollbar-color: #000080 #f1f5f9;
}

.luxury-row::-webkit-scrollbar {
    height: 6px;
}

.luxury-row::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 10px;
}

.luxury-row::-webkit-scrollbar-thumb {
    background: #000080;
    border-radius: 10px;
}

/* Two cards per row layout */
.luxury-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}

.luxury-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transform: translateY(0);
    min-width: 280px;
    flex: 1;
}

.luxury-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
    border-color: rgba(0, 0, 128, 0.3);
}

.luxury-image {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.luxury-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.7s ease;
}

.luxury-card:hover .luxury-image img {
    transform: scale(1.1);
}

.luxury-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 128, 0.2), rgba(0, 0, 128, 0.7));
    display: flex;
    align-items: center;
    justify-content: center;
}

.luxury-icon {
    width: 80px;
    height: 80px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: all 0.4s ease;
    transform: translateY(0);
}

.luxury-card:hover .luxury-icon {
    transform: translateY(-10px) scale(1.1);
}

.luxury-icon i {
    font-size: 2.2rem;
    color: #000080;
}

.luxury-content {
    padding: 30px;
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
}

.luxury-badge {
    position: absolute;
    top: -15px;
    right: 30px;
    padding: 8px 15px;
    background: #f59e0b;
    color: white;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    z-index: 2;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.luxury-title {
    margin-bottom: 25px;
    text-align: center;
}

.luxury-title h3 {
    font-size: 1.8rem;
    color: #000080;
    margin-bottom: 10px;
    font-weight: 700;
}

.luxury-title p {
    font-size: 1rem;
    color: #64748b;
}

.luxury-features {
    margin-bottom: 30px;
    flex: 1;
}

.luxury-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.luxury-features ul li {
    position: relative;
    padding-left: 35px;
    margin-bottom: 15px;
    font-size: 1rem;
    color: #334155;
    display: flex;
    align-items: center;
}

.luxury-features ul li::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: rgba(0, 0, 128, 0.1);
    border-radius: 50%;
}

.luxury-features ul li::after {
    content: "✓";
    position: absolute;
    left: 7px;
    top: 50%;
    transform: translateY(-50%);
    color: #000080;
    font-weight: bold;
    font-size: 0.9rem;
}

.luxury-btn {
    display: block;
    width: 100%;
    background: linear-gradient(to right, #000080, #3b82f6);
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.4s ease;
    box-shadow: 0 10px 20px rgba(0, 0, 128, 0.2);
    text-align: center;
    margin-top: auto;
}

.luxury-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 128, 0.3);
}

.luxury-btn i {
    margin-left: 8px;
    transition: all 0.3s ease;
}

.luxury-btn:hover i {
    transform: translateX(5px);
}

@media (max-width: 992px) {
    .luxury-grid {
        grid-template-columns: 1fr;
    }
    
    .luxury-header h2 {
        font-size: 2.4rem;
    }
    
    .luxury-row {
        flex-wrap: wrap;
    }
    
    .luxury-card {
        min-width: 100%;
    }
}