/* Basic styles for the visit page */
.visit-hero-section {
    position: relative;
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
    overflow: hidden;
}

.visit-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.visit-hero-content {
    display: flex;
    align-items: center;
    gap: 60px;
}

.hero-left {
    flex: 1;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: rgba(0, 0, 128, 0.1);
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
}

.badge-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: #000080;
    border-radius: 50%;
    color: white;
}

.badge-text {
    font-size: 0.9rem;
    font-weight: 600;
    color: #000080;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.hero-title {
    font-size: 3.5rem;
    line-height: 1.2;
    margin-bottom: 20px;
    color: #1e293b;
}

.title-highlight {
    color: #000080;
}

.hero-subtitle {
    font-size: 1.2rem;
    line-height: 1.6;
    color: #64748b;
    margin-bottom: 30px;
}

.hero-features {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 30px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    background: white;
    padding: 10px 20px;
    border-radius: 50px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.feature-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: #000080;
    border-radius: 50%;
    color: white;
}

.hero-right {
    flex: 1;
}

.main-image {
    width: 100%;
    height: 500px;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 25px rgba(0, 0, 0, 0.15);
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.visit-services-section {
    padding: 80px 0;
    background: #f8fafc;
}

.visit-services-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(0, 0, 128, 0.1);
    color: #000080;
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
    font-weight: 600;
}

.section-header h2 {
    font-size: 2.5rem;
    color: #1e293b;
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.1rem;
    color: #64748b;
    max-width: 700px;
    margin: 0 auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}

.service-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 25px rgba(0, 0, 0, 0.15);
}

.service-icon {
    width: 70px;
    height: 70px;
    background: #000080;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 30px auto 20px;
    color: white;
    font-size: 2rem;
}

.service-card h3 {
    font-size: 1.8rem;
    color: #1e293b;
    margin-bottom: 15px;
    text-align: center;
}

.service-card p {
    font-size: 1rem;
    color: #64748b;
    margin-bottom: 20px;
    padding: 0 30px;
    text-align: center;
}

.service-card ul {
    list-style: none;
    padding: 0 30px;
    margin-bottom: 30px;
}

.service-card ul li {
    position: relative;
    padding-left: 30px;
    margin-bottom: 15px;
    color: #1e293b;
}

.service-card ul li::before {
    content: "✓";
    position: absolute;
    left: 0;
    top: 0;
    color: #000080;
    font-weight: bold;
}

.service-countries {
    padding: 20px 30px;
    border-top: 1px solid #e2e8f0;
}

.country-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.country-tags span {
    padding: 5px 15px;
    background: #f1f5f9;
    border-radius: 50px;
    font-size: 0.9rem;
    color: #1e293b;
}

.service-btn {
    display: block;
    width: calc(100% - 60px);
    margin: 0 30px 30px;
    padding: 15px;
    background: #000080;
    color: white;
    text-align: center;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 600;
}

@media (max-width: 992px) {
    .visit-hero-content {
        flex-direction: column;
    }
    
    .hero-left, .hero-right {
        width: 100%;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .main-image {
        height: 400px;
        margin-top: 40px;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
}