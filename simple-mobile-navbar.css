/* Simple Beautiful Mobile Navbar */

/* Mobile Menu Button - Website Theme Colors */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: 52px;
        height: 52px;
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
        border: none;
        border-radius: 15px;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4);
        position: relative;
        z-index: 1001;
        border: 2px solid rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
    }

    .mobile-menu-btn::before {
        content: '';
        position: absolute;
        inset: -2px;
        background: linear-gradient(135deg, #60a5fa, #34d399, #3b82f6);
        border-radius: 17px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.4s ease;
    }

    .mobile-menu-btn:hover::before {
        opacity: 1;
    }

    .mobile-menu-btn:hover {
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
        transform: translateY(-3px) scale(1.08);
        box-shadow: 0 12px 35px rgba(30, 58, 138, 0.5);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .mobile-menu-btn i {
        font-size: 1.9rem;
        color: white;
        transition: all 0.4s ease;
        font-weight: bold;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .mobile-menu-btn:hover i {
        transform: scale(1.15) rotate(5deg);
        text-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
    }

    .desktop-menu {
        display: none !important;
    }
}

/* Mobile Menu Container - Simple & Clean */
.mobile-menu {
    display: none;
    position: fixed;
    top: -100%;
    left: 0;
    width: 100%;
    background: white;
    z-index: 10001;
    transition: top 0.4s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid #e5e7eb;
}

.mobile-menu.active {
    top: 0;
    display: block;
}

/* Mobile Menu Header - Website Theme Colors */
.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 28px 35px;
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
    border-bottom: 3px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 32px rgba(30, 58, 138, 0.3);
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(20px);
}

.mobile-menu-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #60a5fa, #34d399, #3b82f6);
}

.mobile-logo {
    height: 58px;
    filter: brightness(0) invert(1) drop-shadow(3px 3px 12px rgba(255, 255, 255, 0.4));
    transition: all 0.4s ease;
}

.mobile-logo:hover {
    transform: scale(1.08) rotate(2deg);
    filter: brightness(0) invert(1) drop-shadow(4px 4px 16px rgba(255, 255, 255, 0.6));
}

#menu-close {
    font-size: 2rem;
    color: white;
    cursor: pointer;
    padding: 14px;
    border-radius: 15px;
    transition: all 0.4s ease;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.25);
    width: 52px;
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(15px);
    font-weight: bold;
    position: relative;
    overflow: hidden;
}

#menu-close::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border-radius: 17px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

#menu-close:hover::before {
    opacity: 1;
}

#menu-close:hover {
    background: rgba(239, 68, 68, 0.95);
    color: white;
    border-color: rgba(239, 68, 68, 0.95);
    transform: rotate(90deg) scale(1.15);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.5);
}

/* Mobile Navigation Links - Website Theme Colors */
.mobile-nav-links {
    list-style: none;
    padding: 30px 0;
    margin: 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #e0e7ff 100%);
}

.mobile-nav-links li {
    margin: 0 0 5px 0;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

.mobile-nav-links li::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(30, 58, 138, 0.1) 50%, transparent 100%);
    transition: left 0.6s ease;
    z-index: 1;
}

.mobile-nav-links li:hover::before {
    left: 100%;
}

.mobile-nav-links li:last-child {
    margin-bottom: 0;
}

.mobile-nav-links li a {
    display: flex;
    align-items: center;
    gap: 18px;
    padding: 20px 28px;
    text-decoration: none;
    color: #1e3a8a;
    font-size: 1.15rem;
    font-weight: 650;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #e0e7ff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(30, 58, 138, 0.08);
    z-index: 2;
}

.mobile-nav-links li a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    transform: scaleY(0);
    transition: transform 0.4s ease;
    transform-origin: bottom;
    border-radius: 0 4px 4px 0;
}

.mobile-nav-links li a:hover::before {
    transform: scaleY(1);
}

.mobile-nav-links li a:hover {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e3a8a;
    transform: translateX(8px);
    box-shadow: 0 6px 20px rgba(30, 58, 138, 0.2);
    border-color: #3b82f6;
}

.mobile-nav-links li a.active {
    background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
    color: #1e3a8a;
    border-left: 5px solid #3b82f6;
    font-weight: 700;
    box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
    transform: translateX(3px);
}

.mobile-nav-links li a i {
    font-size: 1.5rem;
    width: 28px;
    text-align: center;
    color: #3b82f6;
    transition: all 0.4s ease;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(96, 165, 250, 0.1));
    border-radius: 8px;
    padding: 8px;
}

.mobile-nav-links li a:hover i,
.mobile-nav-links li a.active i {
    color: #1e3a8a;
    transform: scale(1.2) rotate(5deg);
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.15), rgba(59, 130, 246, 0.15));
    box-shadow: 0 2px 8px rgba(30, 58, 138, 0.2);
}

/* Services Dropdown - Website Theme Colors */
.mobile-services-item {
    position: relative;
}

.mobile-services-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 18px;
    padding: 20px 28px;
    text-decoration: none;
    color: #1e3a8a;
    font-size: 1.15rem;
    font-weight: 650;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #e0e7ff;
    border-radius: 12px;
    margin-bottom: 5px;
    box-shadow: 0 2px 8px rgba(30, 58, 138, 0.08);
    position: relative;
    overflow: hidden;
}

.mobile-services-toggle::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    transform: scaleY(0);
    transition: transform 0.4s ease;
    transform-origin: bottom;
    border-radius: 0 4px 4px 0;
}

.mobile-services-toggle:hover::before {
    transform: scaleY(1);
}

.mobile-services-toggle:hover {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e3a8a;
    transform: translateX(8px);
    box-shadow: 0 6px 20px rgba(30, 58, 138, 0.2);
    border-color: #3b82f6;
}

.mobile-services-toggle i:first-child {
    font-size: 1.5rem;
    width: 28px;
    text-align: center;
    color: #3b82f6;
    transition: all 0.4s ease;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(96, 165, 250, 0.1));
    border-radius: 8px;
    padding: 8px;
}

.mobile-services-toggle:hover i:first-child {
    color: #1e3a8a;
    transform: scale(1.2) rotate(5deg);
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.15), rgba(59, 130, 246, 0.15));
    box-shadow: 0 2px 8px rgba(30, 58, 138, 0.2);
}

.mobile-services-toggle .dropdown-arrow {
    font-size: 1.3rem;
    color: #3b82f6;
    transition: all 0.4s ease;
    font-weight: bold;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(96, 165, 250, 0.1));
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-services-toggle:hover .dropdown-arrow {
    color: #1e3a8a;
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.15), rgba(59, 130, 246, 0.15));
    transform: scale(1.1);
}

.mobile-services-item.active .dropdown-arrow {
    transform: rotate(180deg) scale(1.1);
    color: #1e3a8a;
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.2), rgba(59, 130, 246, 0.2));
}

.mobile-services-item.active .mobile-services-toggle {
    background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
    color: #1e3a8a;
    border-left: 5px solid #3b82f6;
    transform: translateX(3px);
    box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
}

.mobile-services-dropdown {
    max-height: 0;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 50%, #dbeafe 100%);
    border-radius: 0 0 15px 15px;
    margin-top: 3px;
    box-shadow: inset 0 3px 8px rgba(30, 58, 138, 0.1);
    border: 2px solid #bfdbfe;
    border-top: none;
}

.mobile-services-item.active .mobile-services-dropdown {
    max-height: 400px;
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.15);
}

.mobile-services-dropdown a {
    display: flex;
    align-items: center;
    gap: 18px;
    padding: 18px 35px 18px 50px;
    text-decoration: none;
    color: #1e3a8a;
    font-size: 1.05rem;
    font-weight: 650;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    margin: 3px 12px;
    border-radius: 10px;
    border: 2px solid #e0e7ff;
    box-shadow: 0 2px 8px rgba(30, 58, 138, 0.08);
    overflow: hidden;
}

.mobile-services-dropdown a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #60a5fa, #34d399);
    transform: scaleY(0);
    transition: transform 0.4s ease;
    transform-origin: bottom;
    border-radius: 0 4px 4px 0;
}

.mobile-services-dropdown a:hover::before {
    transform: scaleY(1);
}

.mobile-services-dropdown a:last-child {
    margin-bottom: 12px;
}

.mobile-services-dropdown a:hover {
    background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
    color: #1e3a8a;
    transform: translateX(10px);
    box-shadow: 0 6px 20px rgba(30, 58, 138, 0.25);
    border-color: #3b82f6;
}

.mobile-services-dropdown a.active {
    background: linear-gradient(135deg, #93c5fd 0%, #60a5fa 100%);
    color: #1e3a8a;
    border-left: 5px solid #3b82f6;
    font-weight: 700;
    box-shadow: 0 6px 20px rgba(30, 58, 138, 0.35);
    transform: translateX(5px);
}

.mobile-services-dropdown a.active::after {
    content: '';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, #34d399, #10b981);
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(52, 211, 153, 0.5);
}

.mobile-services-dropdown a i {
    font-size: 1.3rem;
    width: 32px;
    text-align: center;
    color: #3b82f6;
    transition: all 0.4s ease;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(96, 165, 250, 0.1));
    border-radius: 8px;
    padding: 8px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-services-dropdown a:hover i,
.mobile-services-dropdown a.active i {
    color: #1e3a8a;
    transform: scale(1.25) rotate(8deg);
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.2), rgba(59, 130, 246, 0.2));
    box-shadow: 0 3px 12px rgba(30, 58, 138, 0.25);
}

/* Mobile Apply Button - Website Theme Colors */
.mobile-apply-btn {
    margin: 25px 30px 30px;
    padding: 18px 30px;
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 50%, #f97316 100%);
    color: white;
    border: none;
    border-radius: 15px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    width: calc(100% - 60px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.mobile-apply-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.mobile-apply-btn:hover::before {
    left: 100%;
}

.mobile-apply-btn:hover {
    background: linear-gradient(135deg, #b91c1c 0%, #dc2626 50%, #ea580c 100%);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 35px rgba(220, 38, 38, 0.5);
    border-color: rgba(255, 255, 255, 0.3);
}

.mobile-apply-btn a {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.mobile-apply-btn i {
    font-size: 1.3rem;
    transition: all 0.4s ease;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-apply-btn:hover i {
    transform: translateX(3px) scale(1.1) rotate(5deg);
    background: rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 480px) {
    .mobile-menu-header {
        padding: 15px 20px;
    }
    
    .mobile-logo {
        height: 45px;
    }
    
    .mobile-nav-links li a {
        padding: 12px 20px;
        font-size: 0.95rem;
    }
    
    .mobile-services-toggle {
        padding: 12px 20px;
        font-size: 0.95rem;
    }
    
    .mobile-services-dropdown a {
        padding: 10px 20px 10px 40px;
        font-size: 0.9rem;
    }
    
    .mobile-apply-btn {
        margin: 15px 20px 20px;
        padding: 12px 20px;
        width: calc(100% - 40px);
    }
}
