/* Simple Beautiful Mobile Navbar */

/* Mobile Menu Button - Enhanced Visibility */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        border: none;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
        position: relative;
        z-index: 1001;
        border: 2px solid rgba(255, 255, 255, 0.1);
    }

    .mobile-menu-btn:hover {
        background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
        border-color: rgba(255, 255, 255, 0.2);
    }

    .mobile-menu-btn i {
        font-size: 1.8rem;
        color: white;
        transition: all 0.3s ease;
        font-weight: bold;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .mobile-menu-btn:hover i {
        transform: scale(1.1);
    }

    .desktop-menu {
        display: none !important;
    }
}

/* Mobile Menu Container - Simple & Clean */
.mobile-menu {
    display: none;
    position: fixed;
    top: -100%;
    left: 0;
    width: 100%;
    background: white;
    z-index: 10001;
    transition: top 0.4s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid #e5e7eb;
}

.mobile-menu.active {
    top: 0;
    display: block;
}

/* Mobile Menu Header - Enhanced Visibility */
.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.mobile-logo {
    height: 55px;
    filter: brightness(0) invert(1) drop-shadow(2px 2px 8px rgba(255, 255, 255, 0.3));
    transition: all 0.3s ease;
}

.mobile-logo:hover {
    transform: scale(1.05);
    filter: brightness(0) invert(1) drop-shadow(2px 2px 12px rgba(255, 255, 255, 0.5));
}

#menu-close {
    font-size: 1.8rem;
    color: white;
    cursor: pointer;
    padding: 12px;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    font-weight: bold;
}

#menu-close:hover {
    background: rgba(239, 68, 68, 0.9);
    color: white;
    border-color: rgba(239, 68, 68, 0.9);
    transform: rotate(90deg) scale(1.1);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

/* Mobile Navigation Links - Enhanced Visibility */
.mobile-nav-links {
    list-style: none;
    padding: 25px 0;
    margin: 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.mobile-nav-links li {
    margin: 0 0 3px 0;
    border-radius: 8px;
    overflow: hidden;
}

.mobile-nav-links li:last-child {
    margin-bottom: 0;
}

.mobile-nav-links li a {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 18px 25px;
    text-decoration: none;
    color: #1f2937;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.mobile-nav-links li a:hover {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    color: #1d4ed8;
    transform: translateX(5px);
    box-shadow: 0 3px 12px rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
}

.mobile-nav-links li a.active {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1d4ed8;
    border-left: 4px solid #3b82f6;
    font-weight: 700;
    box-shadow: 0 3px 12px rgba(59, 130, 246, 0.25);
}

.mobile-nav-links li a i {
    font-size: 1.4rem;
    width: 24px;
    text-align: center;
    color: #4b5563;
    transition: all 0.3s ease;
}

.mobile-nav-links li a:hover i,
.mobile-nav-links li a.active i {
    color: #1d4ed8;
    transform: scale(1.15) rotate(3deg);
}

/* Services Dropdown - Enhanced Visibility */
.mobile-services-item {
    position: relative;
}

.mobile-services-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    padding: 18px 25px;
    text-decoration: none;
    color: #1f2937;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
    border-bottom: 1px solid #e5e7eb;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 8px;
    margin-bottom: 2px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.mobile-services-toggle:hover {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    color: #1d4ed8;
    transform: translateX(3px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.mobile-services-toggle i:first-child {
    font-size: 1.3rem;
    width: 24px;
    text-align: center;
    color: #4b5563;
    transition: all 0.3s ease;
}

.mobile-services-toggle:hover i:first-child {
    color: #1d4ed8;
    transform: scale(1.1);
}

.mobile-services-toggle .dropdown-arrow {
    font-size: 1.2rem;
    color: #6b7280;
    transition: all 0.3s ease;
    font-weight: bold;
}

.mobile-services-toggle:hover .dropdown-arrow {
    color: #1d4ed8;
}

.mobile-services-item.active .dropdown-arrow {
    transform: rotate(180deg);
    color: #1d4ed8;
}

.mobile-services-item.active .mobile-services-toggle {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1d4ed8;
    border-left: 4px solid #3b82f6;
}

.mobile-services-dropdown {
    max-height: 0;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-radius: 0 0 12px 12px;
    margin-top: 2px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.mobile-services-item.active .mobile-services-dropdown {
    max-height: 350px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.mobile-services-dropdown a {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 16px 30px 16px 45px;
    text-decoration: none;
    color: #374151;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.5);
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    margin: 2px 8px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.mobile-services-dropdown a:last-child {
    border-bottom: 1px solid rgba(255, 255, 255, 0.5);
    margin-bottom: 8px;
}

.mobile-services-dropdown a:hover {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1d4ed8;
    transform: translateX(8px);
    box-shadow: 0 3px 12px rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
}

.mobile-services-dropdown a.active {
    background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
    color: #1e40af;
    border-left: 4px solid #3b82f6;
    font-weight: 700;
    box-shadow: 0 3px 12px rgba(59, 130, 246, 0.3);
}

.mobile-services-dropdown a i {
    font-size: 1.2rem;
    width: 22px;
    text-align: center;
    color: #6b7280;
    transition: all 0.3s ease;
}

.mobile-services-dropdown a:hover i,
.mobile-services-dropdown a.active i {
    color: #1d4ed8;
    transform: scale(1.15);
}

/* Mobile Apply Button - Simple & Beautiful */
.mobile-apply-btn {
    margin: 20px 25px 25px;
    padding: 15px 25px;
    background: #2563eb;
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: calc(100% - 50px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 2px 10px rgba(37, 99, 235, 0.3);
}

.mobile-apply-btn:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.4);
}

.mobile-apply-btn a {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
}

.mobile-apply-btn i {
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 480px) {
    .mobile-menu-header {
        padding: 15px 20px;
    }
    
    .mobile-logo {
        height: 45px;
    }
    
    .mobile-nav-links li a {
        padding: 12px 20px;
        font-size: 0.95rem;
    }
    
    .mobile-services-toggle {
        padding: 12px 20px;
        font-size: 0.95rem;
    }
    
    .mobile-services-dropdown a {
        padding: 10px 20px 10px 40px;
        font-size: 0.9rem;
    }
    
    .mobile-apply-btn {
        margin: 15px 20px 20px;
        padding: 12px 20px;
        width: calc(100% - 40px);
    }
}
