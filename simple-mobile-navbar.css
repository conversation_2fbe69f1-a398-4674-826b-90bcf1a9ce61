/* Simple Beautiful Mobile Navbar */

/* Mobile Menu Button - Simple & Beautiful */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: 45px;
        height: 45px;
        background: #2563eb;
        border: none;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(37, 99, 235, 0.3);
    }

    .mobile-menu-btn:hover {
        background: #1d4ed8;
        transform: scale(1.05);
        box-shadow: 0 4px 15px rgba(37, 99, 235, 0.4);
    }

    .mobile-menu-btn i {
        font-size: 1.5rem;
        color: white;
    }

    .desktop-menu {
        display: none !important;
    }
}

/* Mobile Menu Container - Simple & Clean */
.mobile-menu {
    display: none;
    position: fixed;
    top: -100%;
    left: 0;
    width: 100%;
    background: white;
    z-index: 10001;
    transition: top 0.4s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid #e5e7eb;
}

.mobile-menu.active {
    top: 0;
    display: block;
}

/* Mobile Menu Header - Simple & Clean */
.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
}

.mobile-logo {
    height: 50px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

#menu-close {
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    background: white;
    border: 1px solid #e5e7eb;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#menu-close:hover {
    background: #ef4444;
    color: white;
    border-color: #ef4444;
    transform: rotate(90deg);
}

/* Mobile Navigation Links - Simple & Beautiful */
.mobile-nav-links {
    list-style: none;
    padding: 20px 0;
    margin: 0;
    background: white;
}

.mobile-nav-links li {
    margin: 0;
    border-bottom: 1px solid #f3f4f6;
}

.mobile-nav-links li:last-child {
    border-bottom: none;
}

.mobile-nav-links li a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px 25px;
    text-decoration: none;
    color: #374151;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.mobile-nav-links li a:hover {
    background: #f3f4f6;
    color: #2563eb;
    padding-left: 30px;
}

.mobile-nav-links li a.active {
    background: #eff6ff;
    color: #2563eb;
    border-left: 4px solid #2563eb;
    font-weight: 600;
}

.mobile-nav-links li a i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
    color: #6b7280;
    transition: all 0.3s ease;
}

.mobile-nav-links li a:hover i,
.mobile-nav-links li a.active i {
    color: #2563eb;
    transform: scale(1.1);
}

/* Services Dropdown - Simple & Clean */
.mobile-services-item {
    position: relative;
}

.mobile-services-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    padding: 15px 25px;
    text-decoration: none;
    color: #374151;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    border-bottom: 1px solid #f3f4f6;
}

.mobile-services-toggle:hover {
    background: #f3f4f6;
    color: #2563eb;
}

.mobile-services-toggle i:first-child {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
    color: #6b7280;
}

.mobile-services-toggle .dropdown-arrow {
    font-size: 1rem;
    color: #9ca3af;
    transition: transform 0.3s ease;
}

.mobile-services-item.active .dropdown-arrow {
    transform: rotate(180deg);
}

.mobile-services-dropdown {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #f9fafb;
}

.mobile-services-item.active .mobile-services-dropdown {
    max-height: 300px;
}

.mobile-services-dropdown a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 25px 12px 50px;
    text-decoration: none;
    color: #6b7280;
    font-size: 0.95rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border-bottom: 1px solid #f3f4f6;
}

.mobile-services-dropdown a:last-child {
    border-bottom: none;
}

.mobile-services-dropdown a:hover {
    background: #eff6ff;
    color: #2563eb;
    padding-left: 55px;
}

.mobile-services-dropdown a i {
    font-size: 1rem;
    width: 18px;
    text-align: center;
    color: #9ca3af;
}

.mobile-services-dropdown a:hover i {
    color: #2563eb;
}

/* Mobile Apply Button - Simple & Beautiful */
.mobile-apply-btn {
    margin: 20px 25px 25px;
    padding: 15px 25px;
    background: #2563eb;
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: calc(100% - 50px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 2px 10px rgba(37, 99, 235, 0.3);
}

.mobile-apply-btn:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.4);
}

.mobile-apply-btn a {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
}

.mobile-apply-btn i {
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 480px) {
    .mobile-menu-header {
        padding: 15px 20px;
    }
    
    .mobile-logo {
        height: 45px;
    }
    
    .mobile-nav-links li a {
        padding: 12px 20px;
        font-size: 0.95rem;
    }
    
    .mobile-services-toggle {
        padding: 12px 20px;
        font-size: 0.95rem;
    }
    
    .mobile-services-dropdown a {
        padding: 10px 20px 10px 40px;
        font-size: 0.9rem;
    }
    
    .mobile-apply-btn {
        margin: 15px 20px 20px;
        padding: 12px 20px;
        width: calc(100% - 40px);
    }
}
