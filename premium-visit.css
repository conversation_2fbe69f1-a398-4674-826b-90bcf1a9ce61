/* Premium Visit Page Styles */
:root {
    --primary-color: #0f172a;
    --secondary-color: #3b82f6;
    --accent-color: #f59e0b;
    --text-light: #f8fafc;
    --text-dark: #1e293b;
    --bg-light: #f8fafc;
    --bg-dark: #0f172a;
    --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.25);
    --gradient-primary: linear-gradient(135deg, #0f172a, #1e293b);
    --gradient-secondary: linear-gradient(135deg, #3b82f6, #60a5fa);
    --gradient-accent: linear-gradient(135deg, #f59e0b, #fbbf24);
}

body {
    font-family: 'Inter', sans-serif;
    color: var(--text-dark);
    background-color: var(--bg-light);
    overflow-x: hidden;
}

/* Premium Hero Section */
.premium-hero-section {
    position: relative;
    min-height: 100vh;
    background: var(--gradient-primary);
    overflow: hidden;
    padding: 120px 0 80px;
}

.premium-hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    animation: float 15s infinite ease-in-out;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0) translateX(0);
    }
    25% {
        transform: translateY(-30px) translateX(15px);
    }
    50% {
        transform: translateY(-15px) translateX(30px);
    }
    75% {
        transform: translateY(-25px) translateX(-15px);
    }
}

.premium-hero-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 30px;
    position: relative;
    z-index: 2;
}

.premium-hero-content {
    display: flex;
    align-items: center;
    gap: 60px;
}

.premium-hero-left {
    flex: 1;
    color: var(--text-light);
}

.premium-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 10px 20px;
    border-radius: 50px;
    margin-bottom: 30px;
    font-weight: 600;
}

.premium-badge-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background: var(--accent-color);
    border-radius: 50%;
    color: var(--text-dark);
}

.premium-badge-icon i {
    font-size: 1rem;
}

.premium-badge-text {
    font-size: 0.9rem;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.premium-hero-title {
    font-size: 4.5rem;
    line-height: 1.1;
    margin-bottom: 30px;
    font-weight: 800;
}

.title-main {
    display: block;
    margin-bottom: 10px;
}

.title-highlight {
    display: block;
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.premium-hero-subtitle {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 40px;
    max-width: 600px;
    opacity: 0.9;
}

.premium-hero-subtitle strong {
    color: var(--accent-color);
    font-weight: 600;
}

.premium-features {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 40px;
}

.premium-feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    padding: 10px 20px;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.premium-feature-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-3px);
}

.premium-feature-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: var(--accent-color);
    border-radius: 50%;
}

.premium-feature-icon i {
    font-size: 0.8rem;
    color: var(--text-dark);
}

.premium-hero-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 40px;
}

.premium-stat-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.premium-stat-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-5px);
}

.premium-stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: var(--gradient-accent);
    border-radius: 12px;
    flex-shrink: 0;
}

.premium-stat-icon i {
    font-size: 1.5rem;
    color: var(--text-dark);
}

.premium-stat-content {
    flex: 1;
}

.premium-stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.premium-stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.premium-cta-section {
    margin-top: 40px;
}

.premium-cta-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.premium-primary-cta, .premium-secondary-cta {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 30px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.premium-primary-cta {
    background: var(--gradient-accent);
    color: var(--text-dark);
    box-shadow: 0 10px 20px rgba(245, 158, 11, 0.3);
}

.premium-primary-cta:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(245, 158, 11, 0.4);
}

.premium-secondary-cta {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-light);
}

.premium-secondary-cta:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
}

.premium-cta-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    flex-shrink: 0;
}

.premium-primary-cta .premium-cta-icon {
    background: rgba(0, 0, 0, 0.1);
}

.premium-cta-icon i {
    font-size: 1rem;
}

.premium-cta-note {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    opacity: 0.7;
}

.premium-cta-note i {
    font-size: 1rem;
    color: var(--accent-color);
}

.premium-hero-right {
    flex: 1;
    position: relative;
}

.premium-hero-visual {
    position: relative;
}

.premium-main-image {
    position: relative;
    width: 100%;
    height: 600px;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.premium-main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}

.premium-main-image:hover img {
    transform: scale(1.05);
}

.premium-image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 30px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    z-index: 2;
}

.premium-overlay-content {
    display: flex;
    justify-content: flex-end;
}

.premium-success-badge {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 10px 20px;
    border-radius: 50px;
    color: var(--text-light);
}

.premium-success-badge i {
    color: var(--accent-color);
}

.premium-floating-cards {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 3;
    pointer-events: none;
}

.premium-info-card {
    position: absolute;
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 15px;
    box-shadow: var(--shadow-lg);
    pointer-events: auto;
    transition: all 0.3s ease;
    max-width: 280px;
}

.premium-info-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.premium-card-1 {
    top: 40px;
    left: -80px;
}

.premium-card-2 {
    bottom: 120px;
    left: -60px;
}

.premium-card-3 {
    top: 180px;
    right: -80px;
}

.premium-card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: var(--gradient-accent);
    border-radius: 12px;
    flex-shrink: 0;
}

.premium-card-icon i {
    font-size: 1.5rem;
    color: var(--text-dark);
}

.premium-card-content {
    flex: 1;
}

.premium-card-content h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-dark);
}

.premium-card-content p {
    font-size: 0.9rem;
    color: #64748b;
}

/* Premium Assessment Form */
.premium-assessment-section {
    position: relative;
    margin-top: -80px;
    z-index: 10;
}

.premium-assessment-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px;
}

.premium-assessment-form-container {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

.premium-assessment-form-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-accent);
}

.premium-assessment-header {
    text-align: center;
    margin-bottom: 30px;
}

.premium-assessment-header h3 {
    font-size: 2rem;
    color: var(--text-dark);
    margin-bottom: 10px;
    font-weight: 700;
}

.premium-assessment-header p {
    font-size: 1.1rem;
    color: #64748b;
}

.premium-assessment-form {
    width: 100%;
}

.premium-form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.premium-form-group {
    position: relative;
}

.premium-form-group input,
.premium-form-group select {
    width: 100%;
    padding: 16px 20px;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    color: var(--text-dark);
    background: #f8fafc;
    transition: all 0.3s ease;
}

.premium-form-group input:focus,
.premium-form-group select:focus {
    border-color: var(--accent-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.premium-form-group input::placeholder,
.premium-form-group select::placeholder {
    color: #94a3b8;
}

.premium-assessment-submit {
    width: 100%;
    padding: 16px;
    background: var(--gradient-accent);
    color: var(--text-dark);
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.premium-assessment-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(245, 158, 11, 0.2);
}

.premium-assessment-submit i {
    font-size: 1.2rem;
}

/* Responsive styles */
@media (max-width: 1200px) {
    .premium-hero-title {
        font-size: 3.5rem;
    }
    
    .premium-hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .premium-main-image {
        height: 500px;
    }
    
    .premium-card-1 {
        left: -40px;
    }
    
    .premium-card-3 {
        right: -40px;
    }
    
    .premium-form-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 992px) {
    .premium-hero-content {
        flex-direction: column;
    }
    
    .premium-hero-left, .premium-hero-right {
        width: 100%;
    }
    
    .premium-hero-title {
        font-size: 3rem;
    }
    
    .premium-main-image {
        height: 400px;
        margin-top: 40px;
    }
    
    .premium-floating-cards {
        display: none;
    }
    
    .premium-assessment-section {
        margin-top: -40px;
    }
}

@media (max-width: 768px) {
    .premium-hero-section {
        padding: 100px 0 60px;
    }
    
    .premium-hero-title {
        font-size: 2.5rem;
    }
    
    .premium-hero-stats {
        grid-template-columns: 1fr;
    }
    
    .premium-cta-buttons {
        flex-direction: column;
    }
    
    .premium-form-grid {
        grid-template-columns: 1fr;
    }
    
    .premium-assessment-form-container {
        padding: 30px 20px;
    }
}

/* Animation classes */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scale-in.visible {
    opacity: 1;
    transform: scale(1);
}