/* Professional Mobile Menu UI - Consistent Across All Pages */

/* Mobile Menu Button - Professional Design */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        border: none;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
        position: relative;
        z-index: 1001;
    }

    .mobile-menu-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
        background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
    }

    .mobile-menu-btn i {
        font-size: 1.8rem;
        color: white;
        transition: all 0.3s ease;
    }

    .desktop-menu {
        display: none !important;
    }
}

/* Mobile Menu Container - Professional Design */
.mobile-menu {
    display: none;
    position: fixed;
    top: -100%;
    left: 0;
    width: 100%;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    z-index: 10001;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(20px);
    border-bottom: 2px solid rgba(30, 64, 175, 0.1);
    max-height: 100vh;
    overflow-y: auto;
}

.mobile-menu.active {
    top: 0;
    display: block;
}

/* Mobile Menu Content */
.mobile-menu-content {
    padding: 0;
    max-height: 100vh;
    overflow-y: auto;
}

/* Mobile Menu Header - Professional Design */
.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.mobile-logo {
    height: 60px;
    filter: brightness(0) invert(1) drop-shadow(2px 2px 8px rgba(255, 255, 255, 0.3));
    transition: all 0.3s ease;
}

.mobile-logo:hover {
    transform: scale(1.05);
    filter: brightness(0) invert(1) drop-shadow(2px 2px 12px rgba(255, 255, 255, 0.5));
}

#menu-close {
    font-size: 1.8rem;
    color: white;
    cursor: pointer;
    padding: 12px;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

#menu-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg) scale(1.1);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

/* Mobile Navigation Links - Professional Design */
.mobile-nav-links {
    list-style: none;
    padding: 30px 25px;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 15px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    min-height: calc(100vh - 200px);
}

.mobile-nav-links li {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border-radius: 15px;
}

.mobile-nav-links li::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(30, 64, 175, 0.1) 50%, transparent 100%);
    transition: left 0.6s ease;
    z-index: 1;
}

.mobile-nav-links li:hover::before {
    left: 100%;
}

.mobile-nav-links li:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.15);
}

.mobile-nav-links li a {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 18px 20px;
    text-decoration: none;
    color: #374151;
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 2;
    border: 1px solid rgba(30, 64, 175, 0.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border-left: 4px solid transparent;
}

.mobile-nav-links li a:hover {
    color: white;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
    border-left-color: #60a5fa;
}

.mobile-nav-links li a.active {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
    border-left-color: #60a5fa;
    font-weight: 700;
}

.mobile-nav-links li a i {
    font-size: 1.3rem;
    width: 24px;
    text-align: center;
    transition: all 0.3s ease;
}

.mobile-nav-links li a:hover i,
.mobile-nav-links li a.active i {
    transform: scale(1.15) rotate(3deg);
}

/* Mobile Apply Button - Professional Design */
.mobile-apply-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin: 25px 25px 30px;
    padding: 18px 30px;
    border-radius: 15px;
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
    color: white;
    border: none;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: calc(100% - 50px);
    text-transform: none;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
    text-decoration: none;
}

.mobile-apply-btn a {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
    justify-content: center;
}

.mobile-apply-btn:hover {
    background: linear-gradient(135deg, #b91c1c 0%, #dc2626 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
}

.mobile-apply-btn:active {
    transform: translateY(0);
}

.mobile-apply-btn i {
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.mobile-apply-btn:hover i {
    transform: translateX(2px) scale(1.1);
}

/* Animation Classes */
@keyframes slideInRight {
    from {
        transform: translateX(50px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.mobile-nav-links li {
    animation: slideInRight 0.3s ease forwards;
}

.mobile-nav-links li:nth-child(1) { animation-delay: 0.1s; }
.mobile-nav-links li:nth-child(2) { animation-delay: 0.15s; }
.mobile-nav-links li:nth-child(3) { animation-delay: 0.2s; }
.mobile-nav-links li:nth-child(4) { animation-delay: 0.25s; }
.mobile-nav-links li:nth-child(5) { animation-delay: 0.3s; }
.mobile-nav-links li:nth-child(6) { animation-delay: 0.35s; }
.mobile-nav-links li:nth-child(7) { animation-delay: 0.4s; }

/* Responsive Design */
@media (max-width: 480px) {
    .mobile-menu-header {
        padding: 20px 25px;
    }
    
    .mobile-logo {
        height: 50px;
    }
    
    .mobile-nav-links {
        padding: 25px 20px;
        gap: 12px;
    }
    
    .mobile-nav-links li a {
        padding: 16px 18px;
        font-size: 1rem;
    }
    
    .mobile-apply-btn {
        margin: 20px 20px 25px;
        padding: 16px 25px;
        width: calc(100% - 40px);
    }
}
