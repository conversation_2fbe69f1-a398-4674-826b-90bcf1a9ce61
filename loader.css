/* Loader Styles */
.loader-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loader-content {
    text-align: center;
    color: white;
}

.lottie-loader .loader-content {
    color: #1e40af;
}

.lottie-loader .loader-text {
    color: #1e40af;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.5);
}

.lottie-loader .loader-subtext {
    color: #3730a3;
    font-weight: 500;
}

.loader-logo {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    animation: pulse 2s infinite;
}

.loader-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.loader-text {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    animation: fadeInOut 2s infinite;
}

.loader-subtext {
    font-size: 14px;
    opacity: 0.8;
    animation: slideUp 1.5s ease-out;
}

.progress-bar {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin: 20px auto;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ffffff, #f0f0f0);
    border-radius: 2px;
    animation: progress 3s ease-out forwards;
}

.lottie-loader .progress-bar {
    background: rgba(30, 64, 175, 0.3);
}

.lottie-loader .progress-fill {
    background: linear-gradient(90deg, #1e40af, #3b82f6);
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes slideUp {
    0% { transform: translateY(20px); opacity: 0; }
    100% { transform: translateY(0); opacity: 0.8; }
}

@keyframes progress {
    0% { width: 0%; }
    100% { width: 100%; }
}

/* Hide loader */
.loader-container.hidden {
    opacity: 0;
    pointer-events: none;
}

/* Lottie themed loader variant */
.lottie-loader {
    background: linear-gradient(135deg, #60a5fa 0%, #93c5fd 50%, #dbeafe 100%);
}

.lottie-animation {
    width: 600px;
    height: 450px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lottie-animation svg {
    max-width: 100%;
    max-height: 100%;
    filter: drop-shadow(0 4px 8px rgba(255, 255, 255, 0.2));
}

/* Fallback loader styles */
.fallback-loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.fallback-loader .airplane {
    width: 60px;
    height: 60px;
    color: white;
    animation: fly 2s ease-in-out infinite;
    margin-bottom: 20px;
    font-size: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes fly {
    0%, 100% { transform: translateX(-10px) rotate(-5deg); }
    50% { transform: translateX(10px) rotate(5deg); }
}

/* Paper Plane Animation */
.paper-plane-animation {
    position: relative;
    width: 300px;
    height: 200px;
    margin: 0 auto 20px;
}

.plane {
    position: absolute;
    font-size: 3rem;
    animation: planeFly 3s ease-in-out infinite;
    z-index: 2;
}

.clouds {
    position: absolute;
    width: 100%;
    height: 100%;
}

.cloud {
    position: absolute;
    font-size: 2rem;
    opacity: 0.7;
    animation: cloudFloat 4s ease-in-out infinite;
}

.cloud1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.cloud2 {
    top: 60%;
    right: 20%;
    animation-delay: 1.5s;
}

.cloud3 {
    top: 40%;
    left: 60%;
    animation-delay: 3s;
}

@keyframes planeFly {
    0% {
        left: -50px;
        top: 50%;
        transform: translateY(-50%) rotate(0deg);
    }
    25% {
        left: 25%;
        top: 30%;
        transform: translateY(-50%) rotate(10deg);
    }
    50% {
        left: 50%;
        top: 70%;
        transform: translateY(-50%) rotate(-5deg);
    }
    75% {
        left: 75%;
        top: 40%;
        transform: translateY(-50%) rotate(15deg);
    }
    100% {
        left: calc(100% + 50px);
        top: 50%;
        transform: translateY(-50%) rotate(0deg);
    }
}

@keyframes cloudFloat {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-10px) scale(1.1);
        opacity: 0.9;
    }
}

/* Mobile responsive */
@media (max-width: 768px) {
    .loader-logo {
        width: 60px;
        height: 60px;
    }

    .lottie-animation {
        width: 350px;
        height: 280px;
        margin: 0 auto;
    }

    .loader-text {
        font-size: 16px;
    }

    .progress-bar {
        width: 150px;
    }
}