/* Premium Cards Styles */
.premium-section {
    padding: 100px 0;
    background: linear-gradient(to bottom, #f8fafc, #ffffff);
    position: relative;
}

.premium-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.premium-header {
    text-align: center;
    margin-bottom: 70px;
    position: relative;
}

.premium-header span {
    display: inline-block;
    padding: 8px 20px;
    background: rgba(0, 0, 128, 0.1);
    color: #000080;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 15px;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.premium-header h2 {
    font-size: 2.8rem;
    color: #000080;
    margin-bottom: 20px;
    font-weight: 700;
}

.premium-header p {
    font-size: 1.2rem;
    color: #64748b;
    max-width: 700px;
    margin: 0 auto;
}

.premium-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.premium-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transform: translateY(0);
}

.premium-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
    border-color: rgba(0, 0, 128, 0.3);
}

.premium-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, #000080, #3b82f6);
    z-index: 2;
}

.premium-image {
    height: 220px;
    overflow: hidden;
    position: relative;
}

.premium-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.7s ease;
}

.premium-card:hover .premium-image img {
    transform: scale(1.1);
}

.premium-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 128, 0.1), rgba(0, 0, 128, 0.6));
    display: flex;
    align-items: center;
    justify-content: center;
}

.premium-icon-wrapper {
    width: 80px;
    height: 80px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: all 0.4s ease;
    transform: translateY(0);
}

.premium-card:hover .premium-icon-wrapper {
    transform: translateY(-10px) scale(1.1);
}

.premium-icon {
    font-size: 2.2rem;
    color: #000080;
}

.premium-content {
    padding: 30px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.premium-title {
    margin-bottom: 25px;
    text-align: center;
}

.premium-title h3 {
    font-size: 1.8rem;
    color: #000080;
    margin-bottom: 10px;
    font-weight: 700;
}

.premium-title p {
    font-size: 1rem;
    color: #64748b;
}

.premium-features {
    margin-bottom: 30px;
    flex: 1;
}

.premium-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.premium-features ul li {
    position: relative;
    padding-left: 35px;
    margin-bottom: 15px;
    font-size: 1rem;
    color: #334155;
    display: flex;
    align-items: center;
}

.premium-features ul li::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: rgba(0, 0, 128, 0.1);
    border-radius: 50%;
}

.premium-features ul li::after {
    content: "✓";
    position: absolute;
    left: 7px;
    top: 50%;
    transform: translateY(-50%);
    color: #000080;
    font-weight: bold;
    font-size: 0.9rem;
}

.premium-footer {
    margin-top: auto;
    padding-top: 25px;
    border-top: 1px solid #f1f5f9;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.premium-countries {
    display: flex;
    gap: 8px;
}

.premium-flag {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid white;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.premium-flag:hover {
    transform: translateY(-3px);
}

.premium-flag img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.premium-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: #000080;
    color: white;
    padding: 12px 25px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.4s ease;
    box-shadow: 0 10px 20px rgba(0, 0, 128, 0.2);
}

.premium-btn:hover {
    background: linear-gradient(to right, #000080, #3b82f6);
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 128, 0.3);
}

.premium-btn i {
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.premium-btn:hover i {
    transform: translateX(5px);
}

.premium-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 8px 15px;
    background: #f59e0b;
    color: white;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    z-index: 2;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

@media (max-width: 992px) {
    .premium-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .premium-header h2 {
        font-size: 2.4rem;
    }
}

@media (max-width: 768px) {
    .premium-grid {
        grid-template-columns: 1fr;
    }
    
    .premium-header h2 {
        font-size: 2rem;
    }
    
    .premium-section {
        padding: 70px 0;
    }
}