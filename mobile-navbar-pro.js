// Professional Mobile Navbar JavaScript

class MobileNavbar {
    constructor() {
        this.menuBtn = null;
        this.menuOverlay = null;
        this.menuContainer = null;
        this.isOpen = false;
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.menuBtn = document.querySelector('.mobile-menu-btn');
        this.menuOverlay = document.querySelector('.mobile-menu-overlay');
        this.menuContainer = document.querySelector('.mobile-menu-container');

        if (!this.menuBtn || !this.menuOverlay || !this.menuContainer) {
            console.warn('Mobile navbar elements not found');
            return;
        }

        this.bindEvents();
        this.setActiveLink();
    }

    bindEvents() {
        // Menu button click
        this.menuBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.toggleMenu();
        });

        // Overlay click
        this.menuOverlay.addEventListener('click', () => {
            this.closeMenu();
        });

        // Menu container click (prevent event bubbling)
        this.menuContainer.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Navigation links click
        const navLinks = document.querySelectorAll('.mobile-nav-link, .mobile-submenu-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                this.closeMenu();
            });
        });

        // Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.closeMenu();
            }
        });

        // Window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768 && this.isOpen) {
                this.closeMenu();
            }
        });

        // Prevent scroll when menu is open
        this.menuOverlay.addEventListener('touchmove', (e) => {
            e.preventDefault();
        }, { passive: false });
    }

    toggleMenu() {
        if (this.isOpen) {
            this.closeMenu();
        } else {
            this.openMenu();
        }
    }

    openMenu() {
        this.isOpen = true;
        this.menuBtn.classList.add('active');
        this.menuOverlay.classList.add('active');
        this.menuContainer.classList.add('active');
        document.body.style.overflow = 'hidden';
        
        // Add animation delay to nav items
        const navItems = document.querySelectorAll('.mobile-nav-item');
        navItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateX(50px)';
            setTimeout(() => {
                item.style.transition = 'all 0.3s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            }, 100 + (index * 50));
        });
    }

    closeMenu() {
        this.isOpen = false;
        this.menuBtn.classList.remove('active');
        this.menuOverlay.classList.remove('active');
        this.menuContainer.classList.remove('active');
        document.body.style.overflow = '';
        
        // Reset nav items animation
        const navItems = document.querySelectorAll('.mobile-nav-item');
        navItems.forEach(item => {
            item.style.transition = '';
            item.style.opacity = '';
            item.style.transform = '';
        });
    }

    setActiveLink() {
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        const navLinks = document.querySelectorAll('.mobile-nav-link, .mobile-submenu-link');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');
            if (href && href.includes(currentPage)) {
                link.classList.add('active');
            }
        });
    }
}

// Initialize mobile navbar
const mobileNavbar = new MobileNavbar();

// Export for global access
window.MobileNavbar = MobileNavbar;
