/* Professional Visit Page Styles */
:root {
    --primary: #000080;
    --primary-light: #3b82f6;
    --accent: #f59e0b;
    --text-dark: #1e293b;
    --text-light: #64748b;
    --bg-light: #f8fafc;
    --bg-dark: #0f172a;
    --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 25px rgba(0, 0, 0, 0.15);
}

body {
    font-family: 'Inter', sans-serif;
    color: var(--text-dark);
    background-color: var(--bg-light);
}

/* Hero Section */
.visit-hero-section {
    position: relative;
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%230f172a' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.floating-icon {
    position: absolute;
    width: 60px;
    height: 60px;
    background: rgba(0, 0, 128, 0.05);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary);
    font-size: 1.5rem;
    animation: float 15s infinite ease-in-out;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-20px);
    }
}

.visit-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.visit-hero-content {
    display: flex;
    align-items: center;
    gap: 60px;
}

.hero-left {
    flex: 1;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: rgba(0, 0, 128, 0.1);
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
}

.badge-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: var(--primary);
    border-radius: 50%;
    color: white;
}

.badge-text {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--primary);
    letter-spacing: 1px;
    text-transform: uppercase;
}

.hero-title {
    font-size: 3.5rem;
    line-height: 1.2;
    margin-bottom: 20px;
    color: var(--text-dark);
}

.title-main {
    display: block;
}

.title-highlight {
    display: block;
    color: var(--primary);
}

.hero-subtitle {
    font-size: 1.2rem;
    line-height: 1.6;
    color: var(--text-light);
    margin-bottom: 30px;
    max-width: 600px;
}

.hero-features {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 30px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    background: white;
    padding: 10px 20px;
    border-radius: 50px;
    box-shadow: var(--shadow-sm);
}

.feature-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: var(--primary);
    border-radius: 50%;
    color: white;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.stat-item {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: rgba(0, 0, 128, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary);
    font-size: 1.5rem;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary);
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-light);
}

.hero-cta-section {
    margin-top: 30px;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.primary-cta, .secondary-cta {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.primary-cta {
    background: var(--primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.primary-cta:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.secondary-cta {
    background: white;
    color: var(--primary);
    border: 1px solid var(--primary);
}

.secondary-cta:hover {
    background: rgba(0, 0, 128, 0.05);
    transform: translateY(-3px);
}

.cta-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
}

.secondary-cta .cta-icon {
    background: rgba(0, 0, 128, 0.1);
}

.cta-note {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    color: var(--text-light);
}

.cta-note i {
    color: var(--primary);
}

.hero-right {
    flex: 1;
}

.hero-visual {
    position: relative;
}

.main-image {
    position: relative;
    width: 100%;
    height: 500px;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}

.main-image:hover img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 30px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
}

.overlay-content {
    display: flex;
    justify-content: flex-end;
}

.success-badge {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
    padding: 10px 20px;
    border-radius: 50px;
    color: white;
}

.success-badge i {
    color: var(--accent);
}

/* Assessment Form */
.quick-assessment-section {
    position: relative;
    margin-top: -50px;
    margin-bottom: 80px;
    z-index: 10;
}

.assessment-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 40px;
    background: white;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    position: relative;
}

.assessment-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--primary);
    border-radius: 20px 20px 0 0;
}

.assessment-header {
    text-align: center;
    margin-bottom: 30px;
}

.assessment-header h3 {
    font-size: 2rem;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.assessment-header p {
    font-size: 1.1rem;
    color: var(--text-light);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.form-group {
    position: relative;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 15px;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    color: var(--text-dark);
    background: #f8fafc;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    border-color: var(--primary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 0, 128, 0.1);
}

.assessment-submit {
    width: 100%;
    padding: 15px;
    background: var(--primary);
    color: white;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.assessment-submit:hover {
    background: #000099;
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

/* Services Section */
.visit-services-section {
    padding: 80px 0;
    background: #f8fafc;
}

.visit-services-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(0, 0, 128, 0.1);
    color: var(--primary);
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
    font-weight: 600;
}

.section-badge i {
    font-size: 1.2rem;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 700px;
    margin: 0 auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}

.service-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.service-icon {
    width: 70px;
    height: 70px;
    background: var(--primary);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 30px auto 20px;
}

.service-icon i {
    font-size: 2rem;
    color: white;
}

.service-card h3 {
    font-size: 1.8rem;
    color: var(--text-dark);
    margin-bottom: 15px;
    text-align: center;
}

.service-card p {
    font-size: 1rem;
    color: var(--text-light);
    margin-bottom: 20px;
    padding: 0 30px;
    text-align: center;
}

.service-card ul {
    list-style: none;
    padding: 0 30px;
    margin-bottom: 30px;
}

.service-card ul li {
    position: relative;
    padding-left: 30px;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.service-card ul li::before {
    content: "✓";
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary);
    font-weight: bold;
}

.service-countries {
    padding: 20px 30px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.service-countries span:first-child {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
}

.country-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.country-tags span {
    padding: 5px 15px;
    background: #f1f5f9;
    border-radius: 50px;
    font-size: 0.9rem;
    color: var(--text-dark);
}

.service-btn {
    display: block;
    width: calc(100% - 60px);
    margin: 0 30px 30px;
    padding: 15px;
    background: var(--primary);
    color: white;
    text-align: center;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.service-btn:hover {
    background: #000099;
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

/* Responsive styles */
@media (max-width: 1200px) {
    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 992px) {
    .visit-hero-content {
        flex-direction: column;
    }
    
    .hero-left, .hero-right {
        width: 100%;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .main-image {
        height: 400px;
        margin-top: 40px;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .form-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .visit-hero-section {
        padding: 100px 0 60px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-stats {
        grid-template-columns: 1fr;
    }
    
    .cta-buttons {
        flex-direction: column;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .assessment-container {
        padding: 30px 20px;
    }
}