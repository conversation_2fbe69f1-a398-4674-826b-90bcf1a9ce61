// Landwise Solutions Style Mobile Navbar JavaScript

(function() {
    'use strict';
    
    console.log('Landwise mobile navbar loading...');
    
    function initLandwiseMobileMenu() {
        console.log('Initializing Landwise mobile menu...');
        
        const menuToggle = document.getElementById('menu-toggle');
        const mobileMenu = document.getElementById('mobile-menu');
        const servicesToggle = document.querySelector('.mobile-services-toggle');
        const servicesItem = document.querySelector('.mobile-services-item');
        
        console.log('Landwise menu elements found:', {
            menuToggle: !!menuToggle,
            mobileMenu: !!mobileMenu,
            servicesToggle: !!servicesToggle,
            servicesItem: !!servicesItem
        });
        
        if (!menuToggle || !mobileMenu) {
            console.warn('Required mobile menu elements not found');
            return;
        }
        
        // Function to open mobile menu - Landwise style
        function openMobileMenu() {
            console.log('Opening Landwise mobile menu');
            mobileMenu.classList.add('active');
            mobileMenu.style.display = 'block';
            
            // Prevent body scroll
            document.body.style.overflow = 'hidden';
            
            // Animate menu
            setTimeout(() => {
                mobileMenu.style.transform = 'translateY(0)';
                mobileMenu.style.opacity = '1';
            }, 10);
        }
        
        // Function to close mobile menu - Landwise style
        function closeMobileMenu() {
            console.log('Closing Landwise mobile menu');
            
            // Animate out
            mobileMenu.style.transform = 'translateY(-100%)';
            mobileMenu.style.opacity = '0';
            
            // Restore body scroll
            document.body.style.overflow = 'auto';
            
            // Close services dropdown
            if (servicesItem) {
                servicesItem.classList.remove('active');
            }
            
            setTimeout(() => {
                mobileMenu.classList.remove('active');
                mobileMenu.style.display = 'none';
            }, 400);
        }
        
        // Menu toggle click
        menuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Landwise menu toggle clicked');
            
            if (mobileMenu.classList.contains('active')) {
                closeMobileMenu();
            } else {
                openMobileMenu();
            }
        });
        
        // Services dropdown functionality
        if (servicesToggle && servicesItem) {
            servicesToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Landwise services toggle clicked');
                servicesItem.classList.toggle('active');
            });
        }
        
        // Close menu when clicking on regular nav links
        const regularNavLinks = document.querySelectorAll('.mobile-nav-links > li > a:not(.mobile-services-toggle)');
        regularNavLinks.forEach(link => {
            link.addEventListener('click', function() {
                console.log('Regular nav link clicked');
                closeMobileMenu();
            });
        });
        
        // Close menu when clicking on services dropdown links
        const servicesDropdownLinks = document.querySelectorAll('.mobile-services-dropdown a');
        servicesDropdownLinks.forEach(link => {
            link.addEventListener('click', function() {
                console.log('Services dropdown link clicked');
                closeMobileMenu();
            });
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (mobileMenu && mobileMenu.classList.contains('active')) {
                if (!mobileMenu.contains(e.target) && !menuToggle.contains(e.target)) {
                    console.log('Clicked outside Landwise menu');
                    closeMobileMenu();
                }
            }
        });
        
        // Close menu on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && mobileMenu && mobileMenu.classList.contains('active')) {
                console.log('Escape key pressed');
                closeMobileMenu();
            }
        });
        
        // Prevent menu from closing when clicking inside
        if (mobileMenu) {
            mobileMenu.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        }
        
        // Set active link based on current page
        setActiveLink();
        
        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768 && mobileMenu.classList.contains('active')) {
                closeMobileMenu();
            }
        });
        
        console.log('Landwise mobile menu initialized successfully');
    }
    
    function setActiveLink() {
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        const navLinks = document.querySelectorAll('.mobile-nav-links a, .mobile-services-dropdown a');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');
            if (href && href.includes(currentPage)) {
                link.classList.add('active');
            }
        });
    }
    
    // Initialize with multiple methods for reliability
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initLandwiseMobileMenu);
    } else {
        initLandwiseMobileMenu();
    }
    
    // Backup initialization
    window.addEventListener('load', function() {
        setTimeout(initLandwiseMobileMenu, 200);
    });
    
    console.log('Landwise mobile navbar script loaded');
})();
