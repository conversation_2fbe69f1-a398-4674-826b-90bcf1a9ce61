/* Enhanced Hero Section Styles */
.hero-enhanced {
    position: relative;
    padding: 120px 0;
    background: linear-gradient(135deg, rgba(0, 0, 128, 0.95), rgba(30, 64, 175, 0.9)), url('https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80');
    background-position: center;
    background-size: cover;
    background-attachment: fixed;
    color: #fff;
    overflow: hidden;
}

.hero-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></svg>') repeat;
    animation: float 15s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0) translateX(0); }
    25% { transform: translateY(-10px) translateX(10px); }
    50% { transform: translateY(0) translateX(0); }
    75% { transform: translateY(10px) translateX(-10px); }
}

.hero-container {
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 15px;
    position: relative;
    z-index: 2;
}

.hero-content {
    display: flex;
    align-items: center;
    gap: 50px;
}

.hero-text {
    flex: 1;
    max-width: 600px;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 20px;
    border-radius: 30px;
    margin-bottom: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: fadeInUp 0.6s ease-out;
}

.hero-badge i {
    font-size: 18px;
    color: #fff;
}

.hero-badge span {
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #fff;
}

.hero-title {
    font-size: 48px;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 25px;
    animation: fadeInUp 0.6s ease-out 0.2s both;
}

.hero-subtitle {
    font-size: 18px;
    line-height: 1.7;
    margin-bottom: 35px;
    opacity: 0.9;
    animation: fadeInUp 0.6s ease-out 0.4s both;
}

.hero-stats {
    display: flex;
    gap: 30px;
    margin-bottom: 35px;
    animation: fadeInUp 0.6s ease-out 0.6s both;
}

.hero-stat {
    display: flex;
    align-items: center;
    gap: 15px;
}

.hero-stat-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-stat-icon i {
    font-size: 20px;
    color: #fff;
}

.hero-stat-text {
    display: flex;
    flex-direction: column;
}

.hero-stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #fff;
}

.hero-stat-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
}

.hero-buttons {
    display: flex;
    gap: 20px;
    animation: fadeInUp 0.6s ease-out 0.8s both;
}

.hero-btn-primary {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: #fff;
    color: var(--primary);
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 700;
    text-decoration: none;
    transition: all 0.4s ease;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.hero-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
}

.hero-btn-primary:hover::before {
    left: 100%;
}

.hero-btn-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.hero-btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 700;
    text-decoration: none;
    transition: all 0.4s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
}

.hero-visual {
    flex: 1;
    position: relative;
    animation: fadeInRight 0.8s ease-out 0.6s both;
}

.hero-image-wrapper {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transform: perspective(1000px) rotateY(-5deg);
    transition: all 0.5s ease;
    border: 5px solid rgba(255, 255, 255, 0.1);
}

.hero-visual:hover .hero-image-wrapper {
    transform: perspective(1000px) rotateY(0deg);
}

.hero-image {
    width: 100%;
    height: auto;
    display: block;
    transition: all 0.5s ease;
}

.hero-visual:hover .hero-image {
    transform: scale(1.05);
}

.hero-badge-floating {
    position: absolute;
    bottom: -20px;
    right: 30px;
    background: #fff;
    color: var(--primary);
    padding: 15px 25px;
    border-radius: 15px;
    font-weight: 700;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 10px;
    animation: fadeInUp 0.6s ease-out 1s both;
    z-index: 2;
}

.hero-badge-floating i {
    font-size: 24px;
    color: var(--secondary);
}

.hero-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    pointer-events: none;
}

.hero-shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.hero-shape-1 {
    width: 300px;
    height: 300px;
    top: -150px;
    right: -150px;
    animation: float-slow 15s infinite alternate;
}

.hero-shape-2 {
    width: 200px;
    height: 200px;
    bottom: -100px;
    left: -100px;
    animation: float-slow 12s infinite alternate-reverse;
}

.hero-shape-3 {
    width: 100px;
    height: 100px;
    top: 30%;
    right: 10%;
    animation: float-slow 10s infinite alternate;
}

@keyframes float-slow {
    0% { transform: translate(0, 0) rotate(0deg); }
    100% { transform: translate(20px, 20px) rotate(10deg); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive styles */
@media (max-width: 1200px) {
    .hero-title {
        font-size: 42px;
    }
    
    .hero-subtitle {
        font-size: 16px;
    }
}

@media (max-width: 992px) {
    .hero-content {
        flex-direction: column;
    }
    
    .hero-text {
        text-align: center;
        max-width: 100%;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .hero-visual {
        margin-top: 40px;
        max-width: 500px;
        width: 100%;
    }
    
    .hero-badge-floating {
        bottom: -15px;
        right: 20px;
    }
}

@media (max-width: 768px) {
    .hero-enhanced {
        padding: 80px 0;
    }
    
    .hero-title {
        font-size: 36px;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: 15px;
    }
    
    .hero-btn-primary, .hero-btn-secondary {
        width: 100%;
        justify-content: center;
    }
    
    .hero-badge-floating {
        position: static;
        margin: 30px auto 0;
        max-width: 250px;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 30px;
    }
    
    .hero-badge {
        padding: 8px 15px;
    }
    
    .hero-badge i {
        font-size: 16px;
    }
    
    .hero-badge span {
        font-size: 12px;
    }
}