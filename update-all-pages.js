// Script to update all pages with new mobile navbar

const fs = require('fs');
const path = require('path');

// List of HTML files to update
const htmlFiles = [
    'contact.html',
    'study.html', 
    'migrate.html',
    'work.html',
    'visit.html',
    'thank-you.html'
];

// Mobile navbar HTML structure
const mobileNavbarHTML = `
    <!-- Professional Mobile Menu Button -->
    <button class="mobile-menu-btn" aria-label="Open Menu">
        <div class="hamburger">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </button>`;

const mobileMenuStructure = `
    <!-- Professional Mobile Menu -->
    <div class="mobile-menu-overlay"></div>
    <div class="mobile-menu-container">
        <div class="mobile-menu-header">
            <img src="images/logo/WING LOGO.png" class="mobile-logo" alt="White Wings Logo">
            <h3 class="mobile-menu-title">Navigation</h3>
        </div>
        
        <nav class="mobile-nav">
            <ul class="mobile-nav-list">
                <li class="mobile-nav-item">
                    <a href="index.html" class="mobile-nav-link">
                        <i class="ri-home-4-line mobile-nav-icon"></i>
                        <span>Home</span>
                    </a>
                </li>
                <li class="mobile-nav-item">
                    <a href="about.html" class="mobile-nav-link">
                        <i class="ri-information-line mobile-nav-icon"></i>
                        <span>About Us</span>
                    </a>
                </li>
                <li class="mobile-nav-item">
                    <a href="study.html" class="mobile-nav-link">
                        <i class="ri-graduation-cap-line mobile-nav-icon"></i>
                        <span>Study Abroad</span>
                    </a>
                </li>
                <li class="mobile-nav-item">
                    <a href="migrate.html" class="mobile-nav-link">
                        <i class="ri-home-heart-line mobile-nav-icon"></i>
                        <span>Migrate</span>
                    </a>
                </li>
                <li class="mobile-nav-item">
                    <a href="work.html" class="mobile-nav-link">
                        <i class="ri-briefcase-line mobile-nav-icon"></i>
                        <span>Work Abroad</span>
                    </a>
                </li>
                <li class="mobile-nav-item">
                    <a href="visit.html" class="mobile-nav-link">
                        <i class="ri-plane-line mobile-nav-icon"></i>
                        <span>Visit</span>
                    </a>
                </li>
                <li class="mobile-nav-item">
                    <a href="contact.html" class="mobile-nav-link">
                        <i class="ri-phone-line mobile-nav-icon"></i>
                        <span>Contact</span>
                    </a>
                </li>
            </ul>
        </nav>
        
        <div class="mobile-apply-section">
            <button class="mobile-apply-btn" onclick="window.location.href='contact.html'">
                <i class="ri-send-plane-line"></i>
                <span>Apply Now</span>
            </button>
        </div>
    </div>`;

// Function to update each HTML file
function updateHTMLFile(filename) {
    try {
        let content = fs.readFileSync(filename, 'utf8');
        
        // Add CSS link if not present
        if (!content.includes('mobile-navbar-pro.css')) {
            content = content.replace(
                /<link href="https:\/\/cdn\.jsdelivr\.net\/npm\/remixicon/,
                '<link rel="stylesheet" href="mobile-navbar-pro.css">\n    <link href="https://cdn.jsdelivr.net/npm/remixicon'
            );
        }
        
        // Replace old mobile menu button
        content = content.replace(
            /<!-- Mobile Menu Button -->[\s\S]*?<\/div>/,
            mobileNavbarHTML
        );
        
        // Replace old mobile menu structure
        content = content.replace(
            /<!-- Mobile Menu -->[\s\S]*?<\/div>\s*<\/div>/,
            ''
        );
        
        // Add new mobile menu structure after </nav-div>
        content = content.replace(
            /<\/nav-div>/,
            '</nav-div>' + mobileMenuStructure
        );
        
        // Add JavaScript if not present
        if (!content.includes('mobile-navbar-pro.js')) {
            content = content.replace(
                /<script src="script\.js"><\/script>/,
                '<script src="mobile-navbar-pro.js"></script>\n    <script src="script.js"></script>'
            );
        }
        
        // Set active state based on filename
        const pageName = path.basename(filename, '.html');
        if (pageName === 'contact') {
            content = content.replace(
                /<a href="contact\.html" class="mobile-nav-link">/,
                '<a href="contact.html" class="mobile-nav-link active">'
            );
        } else if (pageName === 'study') {
            content = content.replace(
                /<a href="study\.html" class="mobile-nav-link">/,
                '<a href="study.html" class="mobile-nav-link active">'
            );
        } else if (pageName === 'migrate') {
            content = content.replace(
                /<a href="migrate\.html" class="mobile-nav-link">/,
                '<a href="migrate.html" class="mobile-nav-link active">'
            );
        } else if (pageName === 'work') {
            content = content.replace(
                /<a href="work\.html" class="mobile-nav-link">/,
                '<a href="work.html" class="mobile-nav-link active">'
            );
        } else if (pageName === 'visit') {
            content = content.replace(
                /<a href="visit\.html" class="mobile-nav-link">/,
                '<a href="visit.html" class="mobile-nav-link active">'
            );
        }
        
        fs.writeFileSync(filename, content);
        console.log(`✅ Updated ${filename}`);
        
    } catch (error) {
        console.error(`❌ Error updating ${filename}:`, error.message);
    }
}

// Update all files
console.log('🚀 Starting mobile navbar update...\n');

htmlFiles.forEach(updateHTMLFile);

console.log('\n✅ All pages updated with professional mobile navbar!');
