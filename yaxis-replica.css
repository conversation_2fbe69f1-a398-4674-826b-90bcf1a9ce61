/* Y-Axis Professional CSS */
:root {
    --primary: #000080;
    --primary-light: #1e40af;
    --primary-dark: #000066;
    --secondary: #f59e0b;
    --accent: #10b981;
    --text-dark: #1e293b;
    --text-light: #64748b;
    --bg-light: #f8fafc;
    --bg-gradient: linear-gradient(135deg, #000080 0%, #1e40af 100%);
    --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.25);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hero Section */
.yaxis-hero {
    background: linear-gradient(135deg, #000080 0%, #1e40af 100%);
    padding: 100px 0;
    position: relative;
    overflow: hidden;
    color: white;
}

.yaxis-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
    opacity: 0.3;
}

.yaxis-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.yaxis-hero-content {
    display: flex;
    align-items: center;
    gap: 40px;
}

.yaxis-hero-left {
    flex: 1;
}

.yaxis-hero-title {
    font-size: 3rem;
    font-weight: 800;
    color: white;
    margin-bottom: 20px;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.yaxis-hero-subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 30px;
    line-height: 1.6;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.yaxis-hero-cta {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: white;
    color: var(--primary);
    padding: 15px 30px;
    border-radius: var(--border-radius);
    font-weight: 700;
    text-decoration: none;
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
    animation: fadeInUp 1s ease-out 0.4s both;
    position: relative;
    overflow: hidden;
}

.yaxis-hero-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.yaxis-hero-cta:hover::before {
    left: 100%;
}

.yaxis-hero-cta:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: var(--shadow-xl);
}

.yaxis-hero-right {
    flex: 1;
    position: relative;
}

.yaxis-hero-image {
    width: 100%;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    position: relative;
    animation: fadeInRight 1s ease-out 0.6s both;
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.yaxis-hero-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,128,0.1), transparent);
    pointer-events: none;
}

.yaxis-hero-image img {
    width: 100%;
    height: auto;
    display: block;
}

/* Visit Services Section */
.visit-services {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e0f7fa 50%, #f0f9ff 100%);
    position: relative;
}

.visit-services::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(59,130,246,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.5;
}

.visit-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.visit-header {
    text-align: center;
    margin-bottom: 50px;
}

.visit-header h2 {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary);
    margin-bottom: 15px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.visit-header p {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 700px;
    margin: 0 auto;
}

.visit-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

/* Enhanced Visit Cards */
.visit-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    position: relative;
    min-height: 400px;
    display: flex;
    flex-direction: column;
}

.visit-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light), #60a5fa);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
}

.visit-card:hover::before {
    transform: scaleX(1);
}

.visit-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: rgba(0, 0, 128, 0.2);
}

.visit-card-header {
    padding: 25px;
    background: linear-gradient(135deg, #f8fafc, #e0f7fa);
    border-bottom: 1px solid rgba(0, 0, 128, 0.1);
    position: relative;
    display: flex;
    align-items: center;
    gap: 20px;
}

.visit-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.4s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 128, 0.3);
    position: relative;
}

.visit-icon::after {
    content: '';
    position: absolute;
    inset: -3px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light), #60a5fa);
    border-radius: 18px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.visit-card:hover .visit-icon::after {
    opacity: 1;
}

.visit-icon i {
    font-size: 1.8rem;
    color: white;
    transition: all 0.3s ease;
}

.visit-card:hover .visit-icon {
    transform: scale(1.1) rotate(10deg);
    box-shadow: 0 12px 35px rgba(0, 0, 128, 0.4);
}

/* Vertical Category Text */
.vertical-category {
    position: absolute;
    right: 20px;
    top: 20px;
    writing-mode: vertical-rl;
    text-orientation: mixed;
    z-index: 2;
}

.category-text {
    font-size: 1.2rem;
    font-weight: 800;
    color: var(--primary);
    letter-spacing: 4px;
    text-transform: uppercase;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.category-text::before {
    content: '';
    position: absolute;
    right: -15px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    border-radius: 2px;
}

.visit-title {
    flex: 1;
}

.visit-title h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

.visit-card:hover .visit-title h3 {
    color: var(--primary-dark);
    transform: translateY(-2px);
}

.visit-title p {
    font-size: 1rem;
    color: var(--text-light);
    margin: 0;
}

/* Enhanced Features */
.visit-features {
    padding: 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.visit-features ul {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
    flex: 1;
}

.visit-features ul li {
    position: relative;
    padding: 12px 0 12px 35px;
    margin-bottom: 5px;
    font-size: 1rem;
    color: var(--text-dark);
    border-bottom: 1px solid rgba(0, 0, 128, 0.05);
    transition: all 0.3s ease;
}

.visit-features ul li:last-child {
    border-bottom: none;
}

.visit-features ul li::before {
    content: "✓";
    position: absolute;
    left: 0;
    top: 12px;
    width: 25px;
    height: 25px;
    background: linear-gradient(135deg, #e0f7fa, #c7d2fe);
    color: var(--primary);
    font-weight: bold;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.visit-features ul li:hover {
    padding-left: 40px;
    color: var(--primary);
}

.visit-features ul li:hover::before {
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: white;
    transform: scale(1.1);
}

/* Enhanced Feature Items */
.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #f8fafc, #e0f7fa);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 128, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    transform: scaleY(0);
    transform-origin: bottom;
    transition: transform 0.3s ease;
}

.feature-item:hover::before {
    transform: scaleY(1);
}

.feature-item:hover {
    background: linear-gradient(135deg, #e0f7fa, #c7d2fe);
    transform: translateX(8px);
    box-shadow: 0 8px 25px rgba(0, 0, 128, 0.15);
}

.feature-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 128, 0.3);
}

.feature-icon i {
    font-size: 1.3rem;
    color: white;
}

.feature-item:hover .feature-icon {
    transform: scale(1.1) rotate(10deg);
    box-shadow: 0 8px 25px rgba(0, 0, 128, 0.4);
}

.feature-content h4 {
    font-size: 1rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 5px;
    line-height: 1.3;
}

.feature-content p {
    font-size: 0.85rem;
    color: var(--text-light);
    line-height: 1.4;
    margin: 0;
}

/* Enhanced Button */
.visit-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: white;
    padding: 15px 25px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 0 25px 25px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 128, 0.3);
}

.visit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.visit-btn:hover::before {
    left: 100%;
}

.visit-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary));
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 0, 128, 0.4);
}

.visit-btn i {
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.visit-btn:hover i {
    transform: translateX(5px);
}

/* Card Color Variations */
.visit-card:nth-child(1) .visit-icon {
    background: linear-gradient(135deg, #22c55e, #16a34a);
}

.visit-card:nth-child(1):hover {
    border-color: rgba(34, 197, 94, 0.3);
}

.visit-card:nth-child(1) .visit-icon::after {
    background: linear-gradient(135deg, #22c55e, #16a34a, #4ade80);
}

.visit-card:nth-child(2) .visit-icon {
    background: linear-gradient(135deg, #f97316, #ea580c);
}

.visit-card:nth-child(2):hover {
    border-color: rgba(249, 115, 22, 0.3);
}

.visit-card:nth-child(2) .visit-icon::after {
    background: linear-gradient(135deg, #f97316, #ea580c, #fb923c);
}

.visit-card:nth-child(3) .visit-icon {
    background: linear-gradient(135deg, #ec4899, #db2777);
}

.visit-card:nth-child(3):hover {
    border-color: rgba(236, 72, 153, 0.3);
}

.visit-card:nth-child(3) .visit-icon::after {
    background: linear-gradient(135deg, #ec4899, #db2777, #f472b6);
}

.work-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed) !important;
}

.work-visa-card:hover {
    border-color: rgba(139, 92, 246, 0.3);
}

.work-icon::after {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed, #a78bfa) !important;
}

/* Animation for cards */
.visit-card.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.visit-card.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Staggered animation for cards */
.visit-card:nth-child(1) { transition-delay: 0.1s; }
.visit-card:nth-child(2) { transition-delay: 0.2s; }
.visit-card:nth-child(3) { transition-delay: 0.3s; }
.visit-card:nth-child(4) { transition-delay: 0.4s; }

/* Enhanced Features Animation */
.enhanced-features .feature-item {
    opacity: 0;
    transform: translateX(-20px);
    animation: slideInLeft 0.6s ease forwards;
}

.enhanced-features .feature-item:nth-child(1) { animation-delay: 0.1s; }
.enhanced-features .feature-item:nth-child(2) { animation-delay: 0.2s; }
.enhanced-features .feature-item:nth-child(3) { animation-delay: 0.3s; }
.enhanced-features .feature-item:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Enhanced Button */
.enhanced-btn {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed) !important;
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.4) !important;
}

.enhanced-btn:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9) !important;
    box-shadow: 0 15px 40px rgba(139, 92, 246, 0.5) !important;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .visit-card-header {
        flex-direction: column;
        text-align: center;
        padding: 20px;
    }
    
    .vertical-category {
        position: static;
        writing-mode: horizontal-tb;
        text-align: center;
        margin-bottom: 15px;
    }
    
    .category-text {
        font-size: 1rem;
        letter-spacing: 2px;
    }
    
    .category-text::before {
        display: none;
    }
    
    .visit-title {
        text-align: center;
    }
    
    .visit-features {
        padding: 20px;
    }
    
    .visit-features ul li {
        padding: 10px 0 10px 30px;
    }
    
    .visit-features ul li::before {
        top: 10px;
        width: 20px;
        height: 20px;
        font-size: 0.8rem;
    }
    
    .feature-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
        padding: 12px;
    }
    
    .feature-icon {
        width: 40px;
        height: 40px;
        margin: 0 auto;
    }
    
    .feature-icon i {
        font-size: 1.1rem;
    }
    
    .feature-content h4 {
        font-size: 0.95rem;
    }
    
    .feature-content p {
        font-size: 0.8rem;
    }
    
    .visit-btn {
        padding: 12px 20px;
        font-size: 0.95rem;
        margin: 0 20px 20px;
    }
}

@media (max-width: 480px) {
    .visit-card {
        margin: 0 10px;
    }
    
    .visit-card-header {
        padding: 15px;
    }
    
    .visit-icon {
        width: 50px;
        height: 50px;
    }
    
    .visit-icon i {
        font-size: 1.5rem;
    }
    
    .category-text {
        font-size: 0.9rem;
        letter-spacing: 1px;
    }
    
    .visit-title h3 {
        font-size: 1.2rem;
    }
    
    .visit-title p {
        font-size: 0.9rem;
    }
    
    .visit-features {
        padding: 15px;
    }
    
    .visit-features ul li {
        font-size: 0.9rem;
        padding: 8px 0 8px 28px;
    }
    
    .visit-features ul li::before {
        top: 8px;
        width: 18px;
        height: 18px;
        font-size: 0.7rem;
    }
    
    .feature-item {
        padding: 10px;
        margin-bottom: 10px;
    }
    
    .feature-icon {
        width: 35px;
        height: 35px;
    }
    
    .feature-icon i {
        font-size: 1rem;
    }
    
    .feature-content h4 {
        font-size: 0.9rem;
    }
    
    .feature-content p {
        font-size: 0.75rem;
    }
    
    .visit-btn {
        padding: 10px 15px;
        font-size: 0.9rem;
        margin: 0 15px 15px;
    }
}

/* Countries Section */
.yaxis-countries {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e0f7fa 50%, #f0f9ff 100%);
    position: relative;
}

.yaxis-countries::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><circle cx="30" cy="30" r="2" fill="%23000080" opacity="0.05"/></svg>') repeat;
}

.yaxis-countries-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 25px;
    max-width: 1000px;
    margin: 0 auto;
}

.yaxis-country-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    text-align: center;
    padding-bottom: 20px;
    position: relative;
    border: 2px solid transparent;
    cursor: pointer;
}

.yaxis-country-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    opacity: 0;
    transition: var(--transition);
    z-index: 1;
}

.yaxis-country-card:hover::before {
    opacity: 0.05;
}

.yaxis-country-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary);
}

.yaxis-country-flag {
    height: 120px;
    overflow: hidden;
    position: relative;
    z-index: 2;
}

.yaxis-country-flag img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.yaxis-country-card:hover .yaxis-country-flag img {
    transform: scale(1.1);
}

.yaxis-country-name {
    padding: 20px 15px 5px;
    position: relative;
    z-index: 2;
}

.yaxis-country-name h4 {
    font-size: 1.1rem;
    color: var(--primary);
    margin: 0;
    font-weight: 700;
    transition: var(--transition);
}

.yaxis-country-card:hover .yaxis-country-name h4 {
    color: var(--primary-dark);
    transform: scale(1.05);
}

/* Process Section */
.yaxis-process {
    padding: 100px 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    position: relative;
}

.yaxis-process::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><polygon points="50,10 90,90 10,90" fill="%23000080" opacity="0.02"/></svg>') repeat;
}

.yaxis-process-steps {
    display: flex;
    justify-content: space-between;
    position: relative;
    margin-top: 60px;
    z-index: 2;
}

.yaxis-process-steps::before {
    content: "";
    position: absolute;
    top: 40px;
    left: 10%;
    width: 80%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), var(--primary-light), var(--primary));
    border-radius: 2px;
    z-index: 1;
    box-shadow: 0 2px 4px rgba(0,0,128,0.2);
}

.yaxis-process-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 3;
    width: 20%;
    animation: fadeInUp 0.8s ease-out;
    animation-fill-mode: both;
}

.yaxis-process-step:nth-child(1) { animation-delay: 0.1s; }
.yaxis-process-step:nth-child(2) { animation-delay: 0.2s; }
.yaxis-process-step:nth-child(3) { animation-delay: 0.3s; }
.yaxis-process-step:nth-child(4) { animation-delay: 0.4s; }
.yaxis-process-step:nth-child(5) { animation-delay: 0.5s; }

.yaxis-step-number {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    border: 3px solid white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    font-weight: 800;
    color: white;
    margin-bottom: 25px;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.yaxis-step-number::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
    transform: rotate(45deg);
    transition: var(--transition);
    opacity: 0;
}

.yaxis-process-step:hover .yaxis-step-number {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-xl);
}

.yaxis-process-step:hover .yaxis-step-number::before {
    opacity: 1;
    animation: shimmer 1s ease-in-out;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.yaxis-step-content {
    text-align: center;
    transition: var(--transition);
}

.yaxis-process-step:hover .yaxis-step-content {
    transform: translateY(-5px);
}

.yaxis-step-content h4 {
    font-size: 1.2rem;
    color: var(--primary);
    margin-bottom: 12px;
    font-weight: 700;
}

.yaxis-step-content p {
    font-size: 0.95rem;
    color: var(--text-light);
    line-height: 1.5;
}

/* Testimonials Section */
.yaxis-testimonials {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e0f7fa 50%, #f0f9ff 100%);
    position: relative;
}

.yaxis-testimonials::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20,20 Q50,10 80,20 Q90,50 80,80 Q50,90 20,80 Q10,50 20,20" fill="%23000080" opacity="0.02"/></svg>') repeat;
}

.yaxis-testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 35px;
    position: relative;
    z-index: 2;
}

.yaxis-testimonial-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 35px;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    position: relative;
    border: 1px solid rgba(0, 0, 128, 0.1);
    overflow: hidden;
}

.yaxis-testimonial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,128,0.02), transparent);
    opacity: 0;
    transition: var(--transition);
}

.yaxis-testimonial-card:hover::before {
    opacity: 1;
}

.yaxis-testimonial-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: rgba(0, 0, 128, 0.2);
}

.yaxis-testimonial-content {
    position: relative;
    padding-left: 35px;
    margin-bottom: 25px;
    z-index: 2;
}

.yaxis-testimonial-content::before {
    content: """;
    position: absolute;
    left: 0;
    top: -15px;
    font-size: 4rem;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: serif;
    line-height: 1;
    opacity: 0.8;
}

.yaxis-testimonial-content p {
    color: var(--text-light);
    line-height: 1.7;
    font-size: 1.05rem;
    font-style: italic;
}

.yaxis-testimonial-author {
    display: flex;
    align-items: center;
}

.yaxis-author-image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
}

.yaxis-author-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.yaxis-author-info h4 {
    font-size: 1.2rem;
    color: var(--primary);
    margin: 0 0 5px;
    font-weight: 700;
}

.yaxis-author-info p {
    font-size: 0.95rem;
    color: var(--text-light);
    margin: 0;
    font-weight: 500;
}

/* FAQ Section */
.yaxis-faq {
    padding: 100px 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    position: relative;
}

.yaxis-faq::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><text x="30" y="35" text-anchor="middle" font-size="20" fill="%23000080" opacity="0.03">?</text></svg>') repeat;
}

.yaxis-faq-list {
    max-width: 900px;
    margin: 60px auto 0;
    position: relative;
    z-index: 2;
}

.yaxis-faq-item {
    background: white;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 128, 0.1);
    overflow: hidden;
    transition: var(--transition);
}

.yaxis-faq-item:hover {
    box-shadow: var(--shadow-md);
    border-color: rgba(0, 0, 128, 0.2);
}

.yaxis-faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    cursor: pointer;
    transition: var(--transition);
    background: linear-gradient(135deg, transparent, rgba(0,0,128,0.02));
}

.yaxis-faq-question:hover {
    background: linear-gradient(135deg, rgba(0,0,128,0.05), rgba(0,0,128,0.02));
}

.yaxis-faq-question h3 {
    font-size: 1.2rem;
    color: var(--primary);
    margin: 0;
    font-weight: 600;
    transition: var(--transition);
}

.yaxis-faq-item:hover .yaxis-faq-question h3 {
    color: var(--primary-dark);
}

.yaxis-faq-question i {
    color: var(--primary);
    font-size: 1.5rem;
    transition: var(--transition);
    background: rgba(0, 0, 128, 0.1);
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.yaxis-faq-item:hover .yaxis-faq-question i {
    background: rgba(0, 0, 128, 0.2);
    transform: scale(1.1);
}

.yaxis-faq-answer {
    padding: 0 30px 25px;
    color: var(--text-light);
    line-height: 1.7;
    display: none;
    font-size: 1.05rem;
    border-top: 1px solid rgba(0, 0, 128, 0.1);
    background: rgba(0, 0, 128, 0.02);
}

.yaxis-faq-item.active .yaxis-faq-question i {
    transform: rotate(180deg) scale(1.1);
    background: var(--primary);
    color: white;
}

.yaxis-faq-item.active .yaxis-faq-answer {
    display: block;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        padding-top: 0;
        padding-bottom: 0;
    }
    to {
        opacity: 1;
        max-height: 200px;
        padding-top: 0;
        padding-bottom: 25px;
    }
}

/* CTA Section */
.yaxis-cta {
    padding: 100px 0;
    background: linear-gradient(135deg, rgba(0, 0, 128, 0.95), rgba(59, 130, 246, 0.9)), url('https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=1500&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.yaxis-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="%23ffffff" opacity="0.1"/></svg>') repeat;
    animation: float 15s ease-in-out infinite;
}

.yaxis-cta-content {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.yaxis-cta-content h2 {
    font-size: 3rem;
    margin-bottom: 25px;
    font-weight: 800;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out;
}

.yaxis-cta-content p {
    font-size: 1.3rem;
    margin-bottom: 40px;
    opacity: 0.95;
    line-height: 1.6;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.yaxis-cta-btn {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: white;
    color: var(--primary);
    padding: 20px 40px;
    border-radius: var(--border-radius);
    font-weight: 800;
    font-size: 1.1rem;
    text-decoration: none;
    transition: var(--transition);
    box-shadow: var(--shadow-xl);
    animation: fadeInUp 1s ease-out 0.4s both;
    position: relative;
    overflow: hidden;
}

.yaxis-cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0,0,128,0.1), transparent);
    transition: left 0.6s;
}

.yaxis-cta-btn:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    color: var(--primary-dark);
}

.yaxis-cta-btn:hover::before {
    left: 100%;
}

/* Form Section */
.yaxis-form {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e0f7fa 50%, #f0f9ff 100%);
    position: relative;
}

.yaxis-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect x="10" y="10" width="80" height="80" fill="none" stroke="%23000080" stroke-width="1" opacity="0.05"/></svg>') repeat;
}

.yaxis-form-container {
    display: flex;
    gap: 50px;
    align-items: center;
    position: relative;
    z-index: 2;
}

.yaxis-form-left {
    flex: 1;
}

.yaxis-form-title {
    margin-bottom: 30px;
}

.yaxis-form-title h2 {
    font-size: 2.5rem;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    font-weight: 800;
}

.yaxis-form-title p {
    font-size: 1.2rem;
    color: var(--text-light);
    line-height: 1.6;
}

.yaxis-form-features {
    margin-bottom: 30px;
}

.yaxis-form-feature {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
}

.yaxis-form-feature-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
    flex-shrink: 0;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.yaxis-form-feature:hover .yaxis-form-feature-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-lg);
}

.yaxis-form-feature-text h4 {
    font-size: 1.2rem;
    color: var(--primary);
    margin: 0 0 8px;
    font-weight: 700;
}

.yaxis-form-feature-text p {
    font-size: 1rem;
    color: var(--text-light);
    margin: 0;
    line-height: 1.5;
}

.yaxis-form-right {
    flex: 1;
}

.yaxis-form-box {
    background: white;
    border-radius: var(--border-radius);
    padding: 40px;
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(0, 0, 128, 0.1);
    position: relative;
    overflow: hidden;
}

.yaxis-form-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--primary-light), var(--primary));
    background-size: 200% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

.yaxis-form-box h3 {
    font-size: 1.8rem;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 30px;
    text-align: center;
    font-weight: 800;
    position: relative;
    z-index: 2;
}

.yaxis-form-group {
    margin-bottom: 25px;
    position: relative;
    z-index: 2;
}

.yaxis-form-group label {
    display: block;
    font-size: 1rem;
    color: var(--primary);
    margin-bottom: 8px;
    font-weight: 600;
}

.yaxis-form-group input,
.yaxis-form-group select {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    color: var(--text-dark);
    transition: var(--transition);
    background: #fafafa;
}

.yaxis-form-group input:hover,
.yaxis-form-group select:hover {
    border-color: rgba(0, 0, 128, 0.3);
    background: white;
}

.yaxis-form-group input:focus,
.yaxis-form-group select:focus {
    border-color: var(--primary);
    outline: none;
    box-shadow: 0 0 0 4px rgba(0, 0, 128, 0.15);
    background: white;
    transform: translateY(-2px);
}

.yaxis-form-submit {
    width: 100%;
    padding: 18px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.yaxis-form-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.yaxis-form-submit:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary));
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

.yaxis-form-submit:hover::before {
    left: 100%;
}

.yaxis-form-submit:active {
    transform: translateY(-1px) scale(1.01);
}

/* Responsive styles */
@media (max-width: 1200px) {
    .yaxis-container {
        padding: 0 30px;
    }
    
    .visit-container {
        padding: 0 30px;
    }
}

@media (max-width: 992px) {
    .yaxis-hero-content {
        flex-direction: column;
        gap: 30px;
    }
    
    .yaxis-hero-title {
        font-size: 2.5rem;
    }
    
    .yaxis-services-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .yaxis-countries-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .yaxis-testimonials-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .yaxis-form-container {
        flex-direction: column;
        gap: 40px;
    }
    
    .yaxis-process-steps {
        flex-direction: column;
        align-items: center;
        gap: 30px;
    }
    
    .yaxis-process-steps::before {
        display: none;
    }
    
    .yaxis-process-step {
        width: 100%;
        max-width: 400px;
        margin-bottom: 0;
    }
    
    .visit-header h2 {
        font-size: 2.3rem;
    }
    
    .yaxis-section-title h2 {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .yaxis-hero {
        padding: 80px 0;
    }
    
    .yaxis-hero-title {
        font-size: 2rem;
    }
    
    .yaxis-hero-subtitle {
        font-size: 1.1rem;
    }
    
    .yaxis-services-grid {
        grid-template-columns: 1fr;
    }
    
    .yaxis-countries-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .yaxis-testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .visit-header h2 {
        font-size: 2rem;
    }
    
    .yaxis-section-title h2 {
        font-size: 1.8rem;
    }
    
    .yaxis-cta-content h2 {
        font-size: 2.2rem;
    }
    
    .yaxis-form-title h2 {
        font-size: 2rem;
    }
    
    .yaxis-form-box {
        padding: 30px 25px;
    }
    
    .visit-card {
        padding: 25px;
    }
    
    .yaxis-testimonial-card {
        padding: 25px;
    }
}

@media (max-width: 480px) {
    .yaxis-container {
        padding: 0 20px;
    }
    
    .visit-container {
        padding: 0 20px;
    }
    
    .yaxis-hero-title {
        font-size: 1.8rem;
    }
    
    .visit-header h2 {
        font-size: 1.8rem;
    }
    
    .yaxis-section-title h2 {
        font-size: 1.6rem;
    }
    
    .yaxis-countries-grid {
        grid-template-columns: 1fr;
    }
    
    .yaxis-cta-content h2 {
        font-size: 1.8rem;
    }
    
    .yaxis-cta-content p {
        font-size: 1.1rem;
    }
    
    .yaxis-form-title h2 {
        font-size: 1.8rem;
    }
    
    .yaxis-form-box {
        padding: 25px 20px;
    }
    
    .visit-card {
        padding: 20px;
    }
    
    .yaxis-testimonial-card {
        padding: 20px;
    }
    
    .yaxis-faq-question {
        padding: 20px;
    }
    
    .yaxis-faq-answer {
        padding: 0 20px 20px;
    }
}