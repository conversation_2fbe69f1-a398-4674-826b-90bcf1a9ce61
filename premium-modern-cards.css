/* Premium Modern Cards Styles */
.premium-modern-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f7fa 100%);
    position: relative;
}

.premium-modern-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23000080' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.5;
    z-index: 0;
}

.premium-modern-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 1;
}

.premium-modern-header {
    text-align: center;
    margin-bottom: 70px;
    position: relative;
}

.premium-modern-header span {
    display: inline-block;
    padding: 8px 20px;
    background: rgba(0, 0, 128, 0.1);
    color: #000080;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 15px;
    letter-spacing: 1px;
    text-transform: uppercase;
    box-shadow: 0 5px 15px rgba(0, 0, 128, 0.1);
}

.premium-modern-header h2 {
    font-size: 2.8rem;
    color: #000080;
    margin-bottom: 20px;
    font-weight: 700;
    position: relative;
    display: inline-block;
}

.premium-modern-header h2::after {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(to right, #000080, #3b82f6);
    border-radius: 3px;
}

.premium-modern-header p {
    font-size: 1.2rem;
    color: #64748b;
    max-width: 700px;
    margin: 30px auto 0;
}

.premium-modern-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
}

.premium-modern-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    position: relative;
    transition: all 0.4s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 100%;
}

.premium-modern-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(to bottom, #000080, #3b82f6);
    z-index: 2;
}

.premium-modern-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
    border-color: rgba(0, 0, 128, 0.3);
}

.premium-modern-card-inner {
    display: flex;
    height: 100%;
}

.premium-modern-card-left {
    width: 40%;
    position: relative;
    overflow: hidden;
}

.premium-modern-card-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.7s ease;
}

.premium-modern-card:hover .premium-modern-card-image {
    transform: scale(1.1);
}

.premium-modern-card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(0, 0, 128, 0.8), rgba(0, 0, 128, 0.5));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.premium-modern-card-icon {
    width: 80px;
    height: 80px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: all 0.4s ease;
    transform: translateY(0) rotate(0deg);
}

.premium-modern-card:hover .premium-modern-card-icon {
    transform: translateY(-10px) rotate(10deg);
}

.premium-modern-card-icon i {
    font-size: 2.2rem;
    color: #000080;
}

.premium-modern-card-right {
    width: 60%;
    padding: 35px;
    display: flex;
    flex-direction: column;
}

.premium-modern-card-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 8px 15px;
    background: linear-gradient(to right, #f59e0b, #f97316);
    color: white;
    border-radius: 30px;
    font-size: 0.9rem;
    font-weight: 600;
    z-index: 2;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.premium-modern-card-title {
    margin-bottom: 25px;
}

.premium-modern-card-title h3 {
    font-size: 2rem;
    color: #000080;
    margin-bottom: 10px;
    font-weight: 700;
    position: relative;
    display: inline-block;
}

.premium-modern-card-title h3::after {
    content: "";
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background: #3b82f6;
    border-radius: 3px;
}

.premium-modern-card-title p {
    font-size: 1.1rem;
    color: #64748b;
}

.premium-modern-card-features {
    margin-bottom: 30px;
    flex: 1;
}

.premium-modern-card-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.premium-modern-card-features ul li {
    position: relative;
    padding-left: 35px;
    margin-bottom: 15px;
    font-size: 1rem;
    color: #334155;
    display: flex;
    align-items: center;
}

.premium-modern-card-features ul li::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: rgba(0, 0, 128, 0.1);
    border-radius: 50%;
}

.premium-modern-card-features ul li::after {
    content: "✓";
    position: absolute;
    left: 7px;
    top: 50%;
    transform: translateY(-50%);
    color: #000080;
    font-weight: bold;
    font-size: 0.9rem;
}

.premium-modern-card-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: linear-gradient(to right, #000080, #3b82f6);
    color: white;
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.4s ease;
    margin-top: auto;
    box-shadow: 0 10px 20px rgba(0, 0, 128, 0.2);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.premium-modern-card-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, #3b82f6, #000080);
    transition: all 0.4s ease;
    z-index: -1;
}

.premium-modern-card-btn:hover::before {
    left: 0;
}

.premium-modern-card-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 128, 0.3);
}

.premium-modern-card-btn i {
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.premium-modern-card-btn:hover i {
    transform: translateX(5px);
}

@media (max-width: 992px) {
    .premium-modern-grid {
        grid-template-columns: 1fr;
    }
    
    .premium-modern-header h2 {
        font-size: 2.4rem;
    }
}

@media (max-width: 768px) {
    .premium-modern-card-inner {
        flex-direction: column;
    }
    
    .premium-modern-card-left {
        width: 100%;
        height: 200px;
    }
    
    .premium-modern-card-right {
        width: 100%;
    }
    
    .premium-modern-section {
        padding: 70px 0;
    }
    
    .premium-modern-header h2 {
        font-size: 2rem;
    }
}