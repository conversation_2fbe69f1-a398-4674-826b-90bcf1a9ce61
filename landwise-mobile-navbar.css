/* Landwise Solutions Style Mobile Navbar - Website Theme Colors */

/* Mobile Navigation - Landwise Style */
@media (max-width: 768px) {
    /* Hide desktop menu */
    .desktop-menu {
        display: none !important;
    }
    
    /* Mobile navbar container */
    nav {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 10000;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(30, 58, 138, 0.1);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    }
    
    nav-div {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 20px;
        height: 70px;
        max-width: 100%;
    }
    
    /* Logo styling */
    nav-div img {
        height: 45px;
        width: auto;
        transition: all 0.3s ease;
    }
    
    /* Mobile menu button - Landwise style */
    .mobile-menu-btn {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: 45px;
        height: 45px;
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(30, 58, 138, 0.3);
        position: relative;
        z-index: 10001;
    }
    
    .mobile-menu-btn:hover {
        background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
        transform: scale(1.05);
        box-shadow: 0 4px 15px rgba(30, 58, 138, 0.4);
    }
    
    .mobile-menu-btn i {
        font-size: 1.6rem;
        color: white;
        transition: all 0.3s ease;
    }
    
    /* Mobile menu overlay - Landwise style */
    .mobile-menu {
        display: none;
        position: fixed;
        top: 70px;
        left: 0;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        z-index: 9999;
        transition: all 0.4s ease;
        transform: translateY(-100%);
        opacity: 0;
    }
    
    .mobile-menu.active {
        display: block;
        transform: translateY(0);
        opacity: 1;
    }
    
    /* Mobile menu content */
    .mobile-menu-content {
        padding: 30px 0;
        height: 100%;
        overflow-y: auto;
    }
    
    /* Mobile navigation links - Landwise style */
    .mobile-nav-links {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: column;
    }
    
    .mobile-nav-links li {
        margin: 0;
        border-bottom: 1px solid rgba(30, 58, 138, 0.1);
    }
    
    .mobile-nav-links li:last-child {
        border-bottom: none;
    }
    
    .mobile-nav-links li a {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 20px 25px;
        text-decoration: none;
        color: #1e3a8a;
        font-size: 1.1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        position: relative;
        background: transparent;
    }
    
    .mobile-nav-links li a::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(135deg, #3b82f6, #60a5fa);
        transform: scaleY(0);
        transition: transform 0.3s ease;
        transform-origin: bottom;
    }
    
    .mobile-nav-links li a:hover::before,
    .mobile-nav-links li a.active::before {
        transform: scaleY(1);
    }
    
    .mobile-nav-links li a:hover {
        background: rgba(30, 58, 138, 0.05);
        color: #1d4ed8;
        padding-left: 30px;
    }
    
    .mobile-nav-links li a.active {
        background: rgba(30, 58, 138, 0.1);
        color: #1d4ed8;
        font-weight: 700;
    }
    
    .mobile-nav-links li a i {
        font-size: 1.3rem;
        width: 24px;
        text-align: center;
        color: #3b82f6;
        transition: all 0.3s ease;
    }
    
    .mobile-nav-links li a:hover i,
    .mobile-nav-links li a.active i {
        color: #1d4ed8;
        transform: scale(1.1);
    }
    
    /* Services dropdown - Landwise style */
    .mobile-services-item {
        position: relative;
    }
    
    .mobile-services-toggle {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px 25px;
        text-decoration: none;
        color: #1e3a8a;
        font-size: 1.1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
        background: transparent;
        border: none;
        width: 100%;
        text-align: left;
    }
    
    .mobile-services-toggle:hover {
        background: rgba(30, 58, 138, 0.05);
        color: #1d4ed8;
    }
    
    .mobile-services-toggle .dropdown-arrow {
        font-size: 1.2rem;
        color: #3b82f6;
        transition: all 0.3s ease;
    }
    
    .mobile-services-item.active .dropdown-arrow {
        transform: rotate(180deg);
        color: #1d4ed8;
    }
    
    .mobile-services-dropdown {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.4s ease;
        background: rgba(30, 58, 138, 0.02);
    }
    
    .mobile-services-item.active .mobile-services-dropdown {
        max-height: 300px;
    }
    
    .mobile-services-dropdown a {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 15px 25px 15px 50px;
        text-decoration: none;
        color: #374151;
        font-size: 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(30, 58, 138, 0.05);
    }
    
    .mobile-services-dropdown a:last-child {
        border-bottom: none;
    }
    
    .mobile-services-dropdown a:hover {
        background: rgba(30, 58, 138, 0.08);
        color: #1d4ed8;
        padding-left: 55px;
    }
    
    .mobile-services-dropdown a.active {
        background: rgba(30, 58, 138, 0.1);
        color: #1d4ed8;
        font-weight: 600;
    }
    
    .mobile-services-dropdown a i {
        font-size: 1.1rem;
        width: 20px;
        text-align: center;
        color: #6b7280;
    }
    
    .mobile-services-dropdown a:hover i,
    .mobile-services-dropdown a.active i {
        color: #1d4ed8;
    }
    
    /* Mobile apply button - Landwise style */
    .mobile-apply-btn {
        margin: 30px 25px 20px;
        padding: 15px 25px;
        background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: calc(100% - 50px);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        box-shadow: 0 2px 10px rgba(220, 38, 38, 0.3);
    }
    
    .mobile-apply-btn:hover {
        background: linear-gradient(135deg, #b91c1c 0%, #dc2626 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
    }
    
    .mobile-apply-btn a {
        color: white;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .mobile-apply-btn i {
        font-size: 1.1rem;
    }
    
    /* Body padding for fixed navbar */
    body {
        padding-top: 70px;
    }
    
    /* Close button - Landwise style */
    #menu-close {
        display: none;
    }
    
    /* Responsive adjustments */
    @media (max-width: 480px) {
        nav-div {
            padding: 10px 15px;
            height: 65px;
        }
        
        nav-div img {
            height: 40px;
        }
        
        .mobile-menu-btn {
            width: 42px;
            height: 42px;
        }
        
        .mobile-menu {
            top: 65px;
            height: calc(100vh - 65px);
        }
        
        body {
            padding-top: 65px;
        }
    }
}
