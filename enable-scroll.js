// Enable normal scrolling
document.addEventListener('DOMContentLoaded', function() {
    // Enable normal scrolling behavior
    document.body.style.overflow = 'auto';
    document.documentElement.style.overflow = 'auto';
    
    // Remove any passive event listeners on wheel events
    const wheelOpt = { passive: true };
    window.addEventListener('wheel', function() {}, wheelOpt);
    
    // Remove any classes that might be interfering with scrolling
    document.documentElement.classList.remove('lenis', 'lenis-smooth');
    
    // Ensure scrolling works with mouse wheel
    document.addEventListener('wheel', function(e) {
        window.scrollBy({
            top: e.deltaY,
            behavior: 'auto'
        });
    });
});