/* Enhanced Home Page Hero Section */

.hero {
    position: relative;
    background: linear-gradient(135deg, #000080 0%, #1e40af 100%);
    overflow: hidden;
    min-height: 100vh;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="1" fill="white" opacity="0.05"/></svg>') repeat;
    z-index: 1;
}

.hero-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 120px 20px 80px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-text {
    color: white;
    animation: fadeInLeft 1s ease-out;
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.hero-text h1 {
    font-size: 4.5rem;
    font-weight: 800;
    margin-bottom: 30px;
    line-height: 1.1;
    background: linear-gradient(to right, #ffffff, #e0f2fe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.hero-text button {
    background: white;
    color: #000080;
    border: none;
    padding: 18px 35px;
    font-size: 1.1rem;
    font-weight: 700;
    border-radius: 50px;
    display: inline-flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    margin-bottom: 30px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.hero-text button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
}

.hero-text button:hover::before {
    left: 100%;
}

.hero-text button:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    background: #f8fafc;
}

.hero-text button i {
    font-size: 1.3rem;
    transition: transform 0.3s ease;
}

.hero-text button:hover i {
    transform: translateX(5px);
}

.hero-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    opacity: 0.9;
    max-width: 90%;
    animation: fadeInUp 1s ease-out 0.4s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slider {
    position: relative;
    animation: fadeInRight 1s ease-out;
    perspective: 1000px;
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.swiper {
    width: 100%;
    height: 500px;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    transform: rotateY(-5deg);
    transition: all 0.5s ease;
    border: 5px solid rgba(255, 255, 255, 0.1);
}

.swiper:hover {
    transform: rotateY(0deg) translateY(-10px);
    box-shadow: 0 35px 60px rgba(0, 0, 0, 0.4);
}

.swiper-slide {
    position: relative;
    overflow: hidden;
}

.swiper-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}

.swiper:hover .swiper-slide img {
    transform: scale(1.05);
}

.slide-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 30px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    transition: all 0.3s ease;
}

.swiper:hover .slide-content {
    padding-bottom: 40px;
}

.slide-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.slide-content p {
    font-size: 1rem;
    opacity: 0.9;
}

.swiper-button-prev,
.swiper-button-next {
    color: white !important;
    background: rgba(0, 0, 0, 0.3);
    width: 50px !important;
    height: 50px !important;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.swiper-button-prev:hover,
.swiper-button-next:hover {
    background: rgba(0, 0, 0, 0.5);
    transform: scale(1.1);
}

.swiper-button-prev::after,
.swiper-button-next::after {
    font-size: 1.5rem !important;
}

/* Flying Airplane Animation */
@keyframes fly {
    0% {
        transform: translateX(-100%) translateY(0) rotate(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateX(300%) translateY(-100px) rotate(10deg);
        opacity: 0;
    }
}

.flying-airplane {
    position: absolute;
    top: 20%;
    left: 0;
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.3);
    animation: fly 15s linear infinite;
    z-index: 1;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .hero-container {
        padding: 100px 20px 60px;
        gap: 40px;
    }
    
    .hero-text h1 {
        font-size: 3.5rem;
    }
}

@media (max-width: 992px) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-text p {
        margin: 0 auto;
    }
    
    .swiper {
        height: 400px;
        transform: rotateY(0);
    }
}

@media (max-width: 768px) {
    .hero-text h1 {
        font-size: 3rem;
    }
    
    .hero-text button {
        padding: 15px 30px;
        font-size: 1rem;
    }
    
    .swiper {
        height: 350px;
    }
}

@media (max-width: 480px) {
    .hero-text h1 {
        font-size: 2.5rem;
    }
    
    .hero-text p {
        font-size: 1rem;
    }
    
    .swiper {
        height: 300px;
    }
}