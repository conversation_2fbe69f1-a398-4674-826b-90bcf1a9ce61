/* Work CTA Section Styles */
.contact-cta-section {
    position: relative;
    padding: 100px 0;
    overflow: hidden;
    background-image: url('https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

.contact-cta-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1;
}

.contact-cta-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
}

.contact-cta-section .section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-cta-section .section-badge i {
    font-size: 1.2rem;
    color: #fbbf24;
}

.contact-cta-section h2 {
    font-size: 3rem;
    margin-bottom: 20px;
    line-height: 1.2;
}

.contact-cta-section p {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.cta-form-container {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.cta-form {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.cta-form-group {
    position: relative;
}

.cta-form-group.full-width {
    grid-column: span 2;
}

.cta-form-input {
    width: 100%;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.cta-form-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.cta-form-input:focus {
    outline: none;
    border-color: #fbbf24;
    background: rgba(255, 255, 255, 0.15);
}

.cta-form-select {
    width: 100%;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 16px;
}

.cta-form-select:focus {
    outline: none;
    border-color: #fbbf24;
    background-color: rgba(255, 255, 255, 0.15);
}

.cta-form-select option {
    background-color: #1e293b;
    color: white;
}

.cta-form-submit {
    width: 100%;
    padding: 15px 20px;
    background: #fbbf24;
    color: #000;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.cta-form-submit:hover {
    background: #f59e0b;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.cta-form-submit i {
    font-size: 1.2rem;
}

.cta-trust-badges {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 40px;
}

.cta-trust-badge {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.cta-trust-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

.cta-trust-icon i {
    font-size: 2rem;
    color: #fbbf24;
}

.cta-trust-text {
    font-size: 1rem;
    font-weight: 500;
}

.cta-trust-subtext {
    font-size: 0.9rem;
    opacity: 0.7;
}

/* Responsive styles */
@media (max-width: 992px) {
    .contact-cta-section h2 {
        font-size: 2.5rem;
    }
    
    .cta-trust-badges {
        flex-wrap: wrap;
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .contact-cta-section {
        padding: 60px 0;
    }
    
    .contact-cta-section h2 {
        font-size: 2rem;
    }
    
    .contact-cta-section p {
        font-size: 1.1rem;
    }
    
    .cta-form {
        grid-template-columns: 1fr;
    }
    
    .cta-form-group.full-width {
        grid-column: span 1;
    }
    
    .cta-form-container {
        padding: 30px 20px;
    }
    
    .cta-trust-badges {
        flex-direction: column;
        align-items: center;
    }
}