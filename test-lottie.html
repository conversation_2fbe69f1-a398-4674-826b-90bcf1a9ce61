<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lottie Test</title>
    <style>
        body {
            margin: 0;
            padding: 50px;
            background: linear-gradient(135deg, #60a5fa 0%, #93c5fd 50%, #dbeafe 100%);
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        .lottie-test {
            width: 400px;
            height: 300px;
            margin: 20px auto;
            border: 2px solid #1e40af;
            border-radius: 10px;
            background: white;
        }
        
        .fallback {
            width: 400px;
            height: 300px;
            margin: 20px auto;
            border: 2px solid #dc2626;
            border-radius: 10px;
            background: #fee2e2;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Lottie Animation Test</h1>
        
        <h2>Test 1: Direct Path</h2>
        <div id="lottie-test-1" class="lottie-test"></div>
        
        <h2>Test 2: Images Folder Path</h2>
        <div id="lottie-test-2" class="lottie-test"></div>
        
        <h2>Test 3: Fallback</h2>
        <div class="fallback">
            <div>
                <i style="font-size: 48px;">✈️</i><br>
                Fallback Animation
            </div>
        </div>
        
        <div id="debug-info" style="margin-top: 30px; padding: 20px; background: white; border-radius: 10px; text-align: left;">
            <h3>Debug Information:</h3>
            <div id="debug-output"></div>
        </div>
    </div>

    <!-- Lottie Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.2/lottie.min.js"></script>
    
    <script>
        const debugOutput = document.getElementById('debug-output');
        
        function log(message) {
            console.log(message);
            debugOutput.innerHTML += '<p>' + message + '</p>';
        }
        
        log('Starting Lottie tests...');
        log('Lottie available: ' + (typeof lottie !== 'undefined'));
        
        // Test 1: Direct path
        try {
            const anim1 = lottie.loadAnimation({
                container: document.getElementById('lottie-test-1'),
                renderer: 'svg',
                loop: true,
                autoplay: true,
                path: './paperplane-animation.json'
            });
            
            anim1.addEventListener('DOMLoaded', () => log('Test 1: Animation loaded successfully'));
            anim1.addEventListener('data_failed', () => log('Test 1: Animation failed to load'));
            
        } catch (error) {
            log('Test 1 Error: ' + error.message);
        }
        
        // Test 2: Images folder path
        try {
            const anim2 = lottie.loadAnimation({
                container: document.getElementById('lottie-test-2'),
                renderer: 'svg',
                loop: true,
                autoplay: true,
                path: './images/Animation - 1752069046690.json'
            });
            
            anim2.addEventListener('DOMLoaded', () => log('Test 2: Animation loaded successfully'));
            anim2.addEventListener('data_failed', () => log('Test 2: Animation failed to load'));
            
        } catch (error) {
            log('Test 2 Error: ' + error.message);
        }
    </script>
</body>
</html>
