/* Simple Cards Styles */
.simple-section {
    padding: 80px 0;
    background: linear-gradient(120deg, #f8fafc 60%, #e0e7ef 100%);
}

.simple-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.simple-header {
    text-align: center;
    margin-bottom: 50px;
}

.simple-header h2 {
    font-size: 2.5rem;
    color: #000080;
    margin-bottom: 15px;
    font-weight: 700;
}

.simple-header p {
    font-size: 1.1rem;
    color: #64748b;
    max-width: 700px;
    margin: 0 auto;
}

.simple-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
    gap: 40px;
    margin-top: 40px;
}

/* --- MODERN GLASSMORPHISM CARD DESIGN FOR VISIT VISA SERVICES --- */
.simple-card {
    background: rgba(255,255,255,0.85);
    border-radius: 28px;
    box-shadow: 0 12px 48px rgba(30,58,138,0.10), 0 1.5px 8px rgba(0,0,0,0.04);
    backdrop-filter: blur(8px);
    border: 1.5px solid rgba(30,58,138,0.10);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 38px 32px 32px 32px;
    position: relative;
    transition: box-shadow 0.35s, transform 0.25s, border 0.25s;
    min-height: 420px;
    overflow: visible;
}
.simple-card:hover {
    box-shadow: 0 24px 64px rgba(30,58,138,0.18), 0 2px 12px rgba(0,0,0,0.08);
    border: 1.5px solid #000080;
    transform: translateY(-10px) scale(1.03);
}
.simple-card-badge {
    position: absolute;
    top: 24px;
    right: 24px;
    background: linear-gradient(90deg, #000080 60%, #1e3a8a 100%);
    color: #fff;
    font-size: 0.95rem;
    font-weight: 700;
    padding: 7px 22px;
    border-radius: 22px;
    letter-spacing: 0.7px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    text-transform: uppercase;
    z-index: 2;
}
.simple-card-icon {
    width: 72px;
    height: 72px;
    background: linear-gradient(135deg, #e0e7ef 0%, #fff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 16px rgba(0,0,0,0.10);
    border: 2.5px solid #000080;
    margin-bottom: 22px;
    font-size: 2.5rem;
    color: #000080;
    position: relative;
    z-index: 1;
}
.simple-card-title {
    font-size: 1.5rem;
    font-weight: 800;
    color: #000080;
    margin-bottom: 8px;
    letter-spacing: 0.7px;
    text-align: center;
}
.simple-card-subtitle {
    font-size: 1.08rem;
    color: #64748b;
    font-weight: 600;
    margin-bottom: 18px;
    text-align: center;
}
.simple-card-features {
    margin-bottom: 28px;
    width: 100%;
}
.simple-card-features ul {
    list-style: none;
    padding: 0;
    margin: 0 0 18px 0;
}
.simple-card-features ul li {
    position: relative;
    padding-left: 32px;
    margin-bottom: 12px;
    font-size: 1.08rem;
    color: #22223b;
    font-weight: 600;
    line-height: 1.5;
}
.simple-card-features ul li::before {
    content: "\2714";
    position: absolute;
    left: 0;
    top: 0;
    color: #1e3a8a;
    font-weight: bold;
    font-size: 1.2rem;
}
.simple-card-btn {
    display: inline-block;
    background: linear-gradient(90deg, #000080 60%, #1e3a8a 100%);
    color: #fff;
    font-weight: 700;
    padding: 13px 38px;
    border-radius: 10px;
    text-decoration: none;
    font-size: 1.08rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    transition: background 0.3s, box-shadow 0.3s, transform 0.2s;
    margin-top: 18px;
    letter-spacing: 0.5px;
    border: none;
}
.simple-card-btn:hover {
    background: linear-gradient(90deg, #1e3a8a 60%, #000080 100%);
    color: #fff;
    box-shadow: 0 8px 24px rgba(0,0,0,0.16);
    transform: translateY(-2px) scale(1.05);
}
@media (max-width: 900px) {
    .simple-section {
        padding: 40px 0;
    }
    .simple-grid {
        gap: 24px;
    }
    .simple-card {
        padding: 28px 12px 24px 12px;
        min-height: 0;
    }
    .simple-card-badge {
        top: 12px;
        right: 12px;
        font-size: 0.85rem;
        padding: 5px 14px;
    }
    .simple-card-icon {
        width: 54px;
        height: 54px;
        font-size: 1.7rem;
    }
}