/* Contact Page Styles */

/* Hero Section */
.contact-hero-section {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 100px 0 80px;
    position: relative;
    overflow: hidden;
}

.contact-hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.contact-hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 2;
}

.contact-hero-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 3;
}

.contact-hero-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.contact-hero-shape-1 {
    width: 300px;
    height: 300px;
    top: -100px;
    left: -100px;
}

.contact-hero-shape-2 {
    width: 200px;
    height: 200px;
    bottom: -50px;
    right: 10%;
}

.contact-hero-shape-3 {
    width: 150px;
    height: 150px;
    top: 30%;
    right: -50px;
}

.contact-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 5;
}

.contact-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    color: white;
}

.contact-hero-text {
    text-align: left;
}

.contact-hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
}

.contact-hero-badge i {
    font-size: 1.2rem;
}

.contact-hero-text h1 {
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.contact-hero-description {
    margin-bottom: 30px;
}

.contact-hero-description p {
    margin-bottom: 15px;
    line-height: 1.7;
    font-size: 1.05rem;
    opacity: 0.9;
}

.contact-hero-actions {
    display: flex;
    gap: 20px;
    margin-top: 30px;
    justify-content: center; /* Center align the buttons */
    width: 100%; /* Ensure full width */
}

.contact-btn-primary, .contact-btn-secondary {
    padding: 14px 24px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.contact-btn-primary {
    background: white;
    color: #1e3a8a;
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.3);
}

.contact-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(255, 255, 255, 0.4);
    background: #f8fafc;
}

.contact-btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.contact-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
    border-color: rgba(255, 255, 255, 0.5);
}

.contact-hero-visual {
    position: relative;
}

.contact-hero-image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.contact-hero-image {
    width: 100%;
    height: auto;
    display: block;
}

.contact-image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 30px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
}

.contact-overlay-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 50px;
    color: white;
    font-weight: 600;
}

.contact-overlay-badge i {
    font-size: 1.2rem;
}

/* Features Area */
.features-area {
    padding: 80px 0;
    background: white;
}

.wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.features-area h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #1e293b;
    margin-bottom: 50px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.feature {
    background: #f8fafc;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
}

.icon-wrapper {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-wrapper img {
    width: 100%;
    height: auto;
}

.feature h3 {
    color: #1e293b;
    font-size: 1.3rem;
    margin-bottom: 15px;
}

.feature p {
    color: #64748b;
    line-height: 1.6;
}

/* Contact Card Section */
.contact-card-section {
    padding: 80px 0;
    background: #f8fafc;
}

.card-wrapper {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 20px;
}

.contact-card-section h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #1e293b;
    margin-bottom: 50px;
}

.contact-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
}

.field {
    position: relative;
}

.field.full {
    grid-column: span 2;
}

label {
    display: block;
    margin-bottom: 8px;
    color: #1e293b;
    font-weight: 500;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 15px;
    color: #64748b;
    font-size: 1.2rem;
}

input, select, textarea {
    width: 100%;
    padding: 12px 12px 12px 45px;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    color: #1e293b;
    background: #f8fafc;
    transition: all 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    background: white;
}

textarea {
    resize: vertical;
    min-height: 100px;
}

.file-input {
    position: relative;
    overflow: hidden;
}

.file-input input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-text {
    display: block;
    padding: 12px 12px 12px 45px;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    color: #64748b;
    background: #f8fafc;
    width: 100%;
}

small {
    display: block;
    margin-top: 5px;
    color: #64748b;
    font-size: 0.8rem;
}

.error-message {
    color: #ef4444;
    font-size: 0.8rem;
    margin-top: 5px;
    min-height: 16px;
}

.btn-submit {
    grid-column: span 2;
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
}

.btn-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(59, 130, 246, 0.6);
}

.btn-submit i {
    font-size: 1.2rem;
}

/* Success Card */
.success-card {
    text-align: center;
    padding: 40px;
}

.success-icon {
    width: 80px;
    height: 80px;
    background: #10b981;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 20px;
}

.success-card h3 {
    font-size: 2rem;
    color: #1e293b;
    margin-bottom: 15px;
}

.success-card p {
    color: #64748b;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.btn-new-request {
    background: #1e293b;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-new-request:hover {
    background: #334155;
    transform: translateY(-3px);
}

/* Map Area */
.map-area {
    padding: 80px 0;
    background: white;
}

.map-area h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #1e293b;
    margin-bottom: 50px;
}

.map-frame {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .contact-hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .contact-hero-text {
        text-align: center;
    }
    
    .contact-hero-text h1 {
        font-size: 2.2rem;
    }
    
    .contact-hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    form {
        grid-template-columns: 1fr;
    }
    
    .field.full {
        grid-column: span 1;
    }
    
    .btn-submit {
        grid-column: span 1;
    }
}