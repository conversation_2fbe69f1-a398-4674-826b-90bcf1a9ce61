/* Professional Mobile Navbar - Modern Design */

/* Mobile Menu Button - Enhanced */
.mobile-menu-btn {
    display: none;
    position: relative;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
    z-index: 1001;
}

.mobile-menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
}

.mobile-menu-btn:active {
    transform: translateY(0);
}

/* Hamburger Icon */
.hamburger {
    position: relative;
    width: 24px;
    height: 18px;
    margin: auto;
}

.hamburger span {
    display: block;
    position: absolute;
    height: 3px;
    width: 100%;
    background: white;
    border-radius: 2px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: all 0.25s ease-in-out;
}

.hamburger span:nth-child(1) {
    top: 0px;
}

.hamburger span:nth-child(2) {
    top: 7px;
}

.hamburger span:nth-child(3) {
    top: 14px;
}

/* Hamburger Animation */
.mobile-menu-btn.active .hamburger span:nth-child(1) {
    top: 7px;
    transform: rotate(135deg);
}

.mobile-menu-btn.active .hamburger span:nth-child(2) {
    opacity: 0;
    left: -60px;
}

.mobile-menu-btn.active .hamburger span:nth-child(3) {
    top: 7px;
    transform: rotate(-135deg);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Mobile Menu Container */
.mobile-menu-container {
    position: fixed;
    top: 0;
    right: -100%;
    width: 320px;
    height: 100vh;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    z-index: 1001;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: -10px 0 30px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.mobile-menu-container.active {
    right: 0;
}

/* Mobile Menu Header */
.mobile-menu-header {
    padding: 30px 25px 20px;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    position: relative;
}

.mobile-menu-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
}

.mobile-logo {
    height: 60px;
    filter: brightness(0) invert(1);
    margin-bottom: 15px;
}

.mobile-menu-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
    opacity: 0.9;
}

/* Mobile Navigation Links */
.mobile-nav-list {
    padding: 25px 0;
    margin: 0;
    list-style: none;
}

.mobile-nav-item {
    margin: 0;
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 18px 25px;
    text-decoration: none;
    color: #374151;
    font-size: 1.1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
    position: relative;
}

.mobile-nav-link:hover {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    color: #1e40af;
    border-left-color: #3b82f6;
    transform: translateX(5px);
}

.mobile-nav-link.active {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
    border-left-color: #1e40af;
    font-weight: 600;
}

.mobile-nav-icon {
    font-size: 1.3rem;
    width: 24px;
    text-align: center;
    color: #6b7280;
    transition: all 0.3s ease;
}

.mobile-nav-link:hover .mobile-nav-icon,
.mobile-nav-link.active .mobile-nav-icon {
    color: #1e40af;
    transform: scale(1.1);
}

/* Services Submenu */
.mobile-services-submenu {
    background: #f9fafb;
    border-top: 1px solid #e5e7eb;
    border-bottom: 1px solid #e5e7eb;
}

.mobile-submenu-item {
    padding: 15px 25px 15px 50px;
    border-bottom: 1px solid #f3f4f6;
}

.mobile-submenu-item:last-child {
    border-bottom: none;
}

.mobile-submenu-link {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    color: #6b7280;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.mobile-submenu-link:hover {
    color: #1e40af;
}

.mobile-submenu-icon {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Mobile Apply Button */
.mobile-apply-section {
    padding: 25px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-top: 1px solid #e5e7eb;
    margin-top: auto;
}

.mobile-apply-btn {
    width: 100%;
    padding: 15px 20px;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.mobile-apply-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
    background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
}

.mobile-apply-btn:active {
    transform: translateY(0);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .desktop-menu {
        display: none !important;
    }
}

@media (max-width: 480px) {
    .mobile-menu-container {
        width: 100%;
        right: -100%;
    }
    
    .mobile-menu-container.active {
        right: 0;
    }
}

/* Animation Classes */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.mobile-nav-item {
    animation: slideInRight 0.3s ease forwards;
}

.mobile-nav-item:nth-child(1) { animation-delay: 0.1s; }
.mobile-nav-item:nth-child(2) { animation-delay: 0.15s; }
.mobile-nav-item:nth-child(3) { animation-delay: 0.2s; }
.mobile-nav-item:nth-child(4) { animation-delay: 0.25s; }
.mobile-nav-item:nth-child(5) { animation-delay: 0.3s; }
.mobile-nav-item:nth-child(6) { animation-delay: 0.35s; }
.mobile-nav-item:nth-child(7) { animation-delay: 0.4s; }
