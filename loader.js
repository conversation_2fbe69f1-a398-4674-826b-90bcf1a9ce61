// Loader JavaScript with Lottie Animation
let lottieAnimation = null;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lottie animation
    initLottieLoader();

    // Show loader on page load
    showLoader();

    // Hide loader after page is fully loaded
    window.addEventListener('load', function() {
        setTimeout(hide<PERSON><PERSON><PERSON>, 2000); // Show loader for at least 2 seconds to enjoy animation
    });
});

function initLottieLoader() {
    // Check if Lottie is available
    if (typeof lottie !== 'undefined') {
        const lottieContainer = document.getElementById('lottie-loader');
        if (lottieContainer) {
            try {
                lottieAnimation = lottie.loadAnimation({
                    container: lottieContainer,
                    renderer: 'svg',
                    loop: true,
                    autoplay: true,
                    path: 'images/Animation - 1752069046690.json', // Path to your animation file
                    rendererSettings: {
                        preserveAspectRatio: 'xMidYMid meet',
                        progressiveLoad: false,
                        hideOnTransparent: true
                    }
                });

                // Add event listeners
                lottieAnimation.addEventListener('complete', function() {
                    console.log('Lottie animation completed');
                });

                lottieAnimation.addEventListener('error', function() {
                    console.error('Lottie animation error');
                    showFallbackLoader();
                });

                console.log('Lottie animation initialized successfully');
            } catch (error) {
                console.error('Error initializing Lottie:', error);
                showFallbackLoader();
            }
        }
    } else {
        console.warn('Lottie library not loaded');
        showFallbackLoader();
    }
}

function showFallbackLoader() {
    const lottieContainer = document.getElementById('lottie-loader');
    if (lottieContainer) {
        lottieContainer.innerHTML = `
            <div class="fallback-loader">
                <div class="airplane">
                    <i class="ri-plane-line"></i>
                </div>
                <div class="loader-spinner"></div>
            </div>
        `;
    }
}

function showLoader() {
    const loader = document.getElementById('loader');
    if (loader) {
        loader.style.display = 'flex';
        document.body.style.overflow = 'hidden'; // Prevent scrolling
    }
}

function hideLoader() {
    const loader = document.getElementById('loader');
    if (loader) {
        loader.classList.add('hidden');
        document.body.style.overflow = 'auto'; // Enable scrolling

        // Stop Lottie animation
        if (lottieAnimation) {
            lottieAnimation.pause();
        }

        // Remove loader from DOM after animation
        setTimeout(() => {
            loader.style.display = 'none';
            if (lottieAnimation) {
                lottieAnimation.destroy();
                lottieAnimation = null;
            }
        }, 500);
    }
}

// Optional: Show loader for navigation
function showLoaderForNavigation() {
    initLottieLoader(); // Reinitialize animation if needed
    showLoader();
    setTimeout(hideLoader, 1500);
}

// Add to any navigation links if needed
document.querySelectorAll('a[href^="#"]').forEach(link => {
    link.addEventListener('click', function() {
        // Optional: Add loader for smooth section transitions
        // showLoaderForNavigation();
    });
});